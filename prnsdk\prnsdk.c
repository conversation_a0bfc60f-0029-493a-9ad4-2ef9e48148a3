/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file prnsdk.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief PrintSystem SDK server for port 443
 */
#include <sys/eventfd.h>
#include <poll.h>
#include <sys/timerfd.h>
#include <curl/curl.h>
#include <curl/easy.h>
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "netmisc.h"
#include "netsts.h"
#include "msgrouter_main.h"
#include "http_task.h"
#include "prnsdk.h"
#include "prnsdk_status.h"
#include "prnsdk_parser.h"
#include "prnsdk_state_manage/prnsdk_state_manage.h"
#include "authority_adapt/authority_adapt.h"
#include "status_manager.h"

#define MSG_PRNSDK_NET_UI_RS_SET_PRINTER 2
#define MSG_PRNSDK_NET_UI_RS_JOB_LIST_UPDATE 1
#define MSG_PRNSDK_NET_UI_RS_QC_UPDATE 3
#define MSG_PRNSDK_NET_UI_RS_REQUEST_BIND_CARD 4
#define MSG_PRNSDK_NET_UI_RS_BIND_CARD_RESULT 5
#define MSG_CTRL_QIO_AUTHZ_CANCEL_JOB 0

#define MSG1_CODE_SCAN_JOB_LIST_UPDATE                6
#define MSG1_CODE_COPY_JOB_WAIT_SCAN_LIST_UPDATE      7
#define MSG1_CODE_COPY_JOB_WAIT_PRINT_LIST_UPDATE     8
#define MSG1_CODE_UPDATE_JOB_HANDLING                 11

#define DEVICE_STATUS_READY 0
#define DEVICE_STATUS_ERROR 1
#define DEVICE_STATUS_RUNNING 2
#define DEVICE_STATUS_WARN  3
#define EVENTFD_AUTH_SUCCESS 0x5a
#define EVENTFD_AUTH_FAIL    0xa5

#define SENTINEL_VAL       -1

#define _autofree_cjson_ __attribute__ ((__cleanup__(cjson_cleanup)))

#define PRINT_JOB_COUNT_MAX  20

#ifndef MAX_HTTP_URL
#define MAX_HTTP_URL        2048
#endif

#define BREAK_VAL_IF(_cond, _log, block) if (_cond) { _log("[LINE:%d] condition ("#_cond"), break. \n", __LINE__); block; break;  }

#define LOG_IF(_cond, _log, fmt, ...)      do { if(_cond) { _log(fmt, ##__VA_ARGS__);} while(0)

#ifdef PRNSDK_ATOMIC_USING_MUTEX  // 使用mutex保证原子性

#define ATOMIC_GET(_member, var)                                             \
    do {                                                                          \
	pi_mutex_lock(s_msg_ret_ctx.mutex);                           \
	(var) = s_msg_ret_ctx->msg_ret_ctx._member;                                \
	pi_mutex_unlock(s_prnsdk_ctx->msg_ret_ctx.mutex);                         \
    } while(0)

#define ATOMIC_SET(_member, val)                                             \
    do {                                                                          \
	pi_mutex_lock(s_msg_ret_ctx.mutex);                           \
	s_msg_ret_ctx._member = (val);                                \
	pi_mutex_unlock(s_msg_ret_ctx.mutex);                         \
    } while(0)

#else // use gcc __atomic buildin, inter-threads no need to synchronize with, 使用__ATOMIC_SEQ_CST最强语义保证内存可见性

#define ATOMIC_GET(var, _member)                                             \
	(var) = __atomic_load_n(_member, __ATOMIC_SEQ_CST)
//	(var) = __atomic_load_n(_member, __ATOMIC_ACQUIRE)

#define ATOMIC_SET(_member, val)                                             \
	__atomic_store_n(_member, (val), __ATOMIC_RELAXED)
//  __atomic_store_n(_member, (val), __ATOMIC_SEQ_CST)
//	__atomic_store_n(_member, (val), __ATOMIC_RELEASE)

/* atomic compare and exchange operation,
 * this compares the content of *_member with the content of *expected,
 * if equal, the operation is a read-modify-write operation that write
 * desired in *_member and return true.
 * if not equal, the operation is a read and the current content of *ptr
 * written into *expected
 */
#define MSG_RET_CTX_COMPARE_EXCHANGE(_member, expected, desired)                  \
	__atomic_compare_exchange_n(_member, expected, desired, false, __ATOMIC_SEQ_CST, __ATOMIC_SEQ_CST)
    //	__atomic_compare_exchange_n(&s_msg_ret_ctx._member, expected, desired, false, __ATOMIC_RELAXED, __ATOMIC_RELAXED)

#endif

#define DEFAULT_HEART_TIME 120
#define PRNSDK_AUTHORIZE_TIMEOUT 60
#define PRNSDK_MAXIMUM_PARALLELISM 10

#define APPLICATION_JSON "application/json; charset=\"utf-8\""
#define TEXT_HTML "text/html; charset=\"utf-8\""

#define SET_MUTEX_PROTECT(block)                              \
    pthread_mutex_lock(&s_prnsdk_ctx->set_mutex);             \
    block                                                     \
    pthread_mutex_unlock(&s_prnsdk_ctx->set_mutex);           \

#define PRNSDK_REQ_DEFINITION(__signature)  \
static int __signature(PRIV_INFO_S* priv, const char** rcontent, const char** rcode, const char** rtype)

#define _SET_BITS(a1, a2, a3, a4, a5, ...)       ((uint64_t)1) << (a1) | ((uint64_t)1) << (a2) | ((uint64_t)1) << (a3) | ((uint64_t)1) << (a4) | ((uint64_t)1) << (a5)
#define SET_BITS(...)                            _SET_BITS(__VA_ARGS__, 62, 62, 62, 62, 62)
#define IS_BIT_SET(mask, a1)                           ( mask & ((uint64_t)1) << (a1) )

#define URL_PREFIX "/PrintSystem"
#define URL_PREFIX_LEN (sizeof(URL_PREFIX) - 1)


#define SEND_MSG_TO_PANEL_AND_WAIT(_msg1, _msg2, _msg3, _ret_token, _retry_time)   \
    send_msg_to_panel_and_wait(_msg1, _msg2, _msg3, _ret_token, _retry_time, #_ret_token)


typedef struct {
        uint32_t tray_1      : 2;    //0 for normal, 1 for near empty, 2 for empty, 3 for open
        uint32_t tray_2      : 2;
        uint32_t tray_3      : 2;
        uint32_t tray_4      : 2;
        uint32_t tray_lct_in : 2;
        uint32_t tray_lct_ex : 2;
        uint32_t device_sts  : 2;
        uint32_t error_code;
} __attribute__((aligned(8)))
PRNSDK_STATUS_S;

typedef struct event_context
{
    char            request_type[128];
    char            request_data[512];
}
EVENT_CTX_S;

typedef struct prnsdk_task_private
{
    char                iobuf[TCP_CHUNK_SIZE];
    size_t              reqtotal;
    size_t              received;
}
PRIV_INFO_S;

/**
 * @brief       print system request callback.
 * @param[in]   priv      : The  PRIV_INFO_S object pointer.
 * @param[out]  rcontent  : json string be returned, must not NULL
 * @param[out]  rcode     : http header code, maybe NULL
 * @param[out]  rtype     : http header type, maybe NULL
 * @return      reply value
 * @retval      == 0      : success\n
 *              == -1    : fail
 */
typedef __attribute__((nonnull(1, 2))) int (*PRNSDK_REQ_FUNC)(PRIV_INFO_S* priv, const char** rcontent, const char** rcode, const char** rtype);

typedef struct prnsdk_url_table
{
    uint64_t            request_method_mask;
    const char*         request_url;
    PRNSDK_REQ_FUNC     request_handle;
}
PRNSDK_URL_TABLE_S;

typedef struct prnsdk_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     tid_prnsdk;
    PI_THREAD_T     tid_heartbeat;
    PI_THREAD_T     tid_notify;

    uint32_t        prnsdk_enabled;
    EVT_MGR_CLI_S   *evt_mgr_cli;

    // follows protected by set_mutex
    pthread_mutex_t set_mutex;
    char            set_url_path[128];
    volatile int    set_heart_time;
    struct          itimerspec heart_tspec;
    int             heart_timerfd;

    // follows protected by login_mutex
    PI_MUTEX_T      login_mutex;
    char            login_username[32];
    char            login_userpassword[32];
    char            login_domain[64];
    char            login_card_username[32];
    char            login_card_userpassword[32];
    char            login_card_domain[64];
    char            login_auth_username[256];
    void*           job_qio_msg[PRINT_JOB_COUNT_MAX];

    // follows protected by job_mutex
    PI_MUTEX_T      job_mutex;
    char            job_printinfo[SDK_PULL_JOBINFO_LEN_MAX];
    char            job_copyinfo[SDK_PULL_JOBINFO_LEN_MAX];
    char            job_scaninfo[SDK_PULL_JOBINFO_LEN_MAX];
    char            job_updated_printinfo[SDK_PULL_JOBINFO_LEN_MAX]; //****新增详细纸张记录（A3,A4,A5等）
    char            job_updated_copyinfo[SDK_PULL_JOBINFO_LEN_MAX];  //****新增详细纸张记录（A3,A4,A5等）
    char            job_updated_scaninfo[SDK_PULL_JOBINFO_LEN_MAX];  //****新增详细纸张记录（A3,A4,A5等）
    char            job_upload_info[SDK_PULL_JOBINFO_LEN_MAX];
    uint32_t        job_update_flag;
    int             auth_efd;
    PI_MUTEX_T      auth_mutex;

} PRNSDK_CTX_S;

typedef struct msg_ret_context
{
    volatile uint32_t   ui_joblist_ret;
    volatile uint32_t   ui_job_handling_ret;
    volatile uint32_t   ui_set_ret;
    volatile uint32_t   ui_qr_ret;
    volatile uint32_t   ui_request_bind_ret;
    volatile uint32_t   login_ret;
    volatile uint32_t   debug_ret;
} MSG_RET_CTX_S;

static PRNSDK_CTX_S*    s_prnsdk_ctx = NULL;
static MSG_RET_CTX_S    s_msg_ret_ctx = { 0 };

static PRNSDK_STATUS_S s_sdk_sts;

static int32_t send_event_handle(const char* request_type, const char *request_data, int retry_flag);
#define send_event(a, b)  send_event_handle(a, b, 0)

__attribute__((always_inline))
static inline void cjson_cleanup(cJSON** ppcjson)
{
    if (*ppcjson)
    {
		cJSON_Delete(*ppcjson);
		*ppcjson = NULL;
    }
}

static int is_prnsdk_url(const char* url)
{
    int ret = 0;

    if ( strncmp(url, URL_PREFIX, URL_PREFIX_LEN ) == 0 )
    {
        ret = 1;
    }

    return ret;
}

static void notify_auth_result(int result)
{
    eventfd_t dummy;

    eventfd_read(s_prnsdk_ctx->auth_efd, &dummy);
    eventfd_write(s_prnsdk_ctx->auth_efd, result);
}

static void sec2itimespec(int seconds, struct itimerspec* its)
{
    its->it_value.tv_sec = seconds;
    its->it_value.tv_nsec = 0;
    its->it_interval.tv_sec = 0;
    its->it_interval.tv_nsec = 0;
}

static void prnsdk_set_enable(uint32_t enable)
{
    enable = !!enable;
    pi_nvram_set(PRNSDK_ID_PRNSDK_ENABLE, VTYPE_UINT, (void*)&enable, sizeof(enable), 1, NULL);
    sync();
    pi_event_mgr_notify(s_prnsdk_ctx->evt_mgr_cli, EVT_TYPE_PRNSDK_ENABLE_MODIFY, (void *)&enable, sizeof(enable));
    s_prnsdk_ctx->prnsdk_enabled = enable;
}

static int prnsdk_hearttime_set(int seconds)
{
    sec2itimespec(seconds, &s_prnsdk_ctx->heart_tspec);
    return timerfd_settime(s_prnsdk_ctx->heart_timerfd, 0, &s_prnsdk_ctx->heart_tspec, NULL);
}

static int prnsdk_heart_stop()
{
    return prnsdk_hearttime_set(0);
}

static int prnsdk_heart_update()
{
    return prnsdk_hearttime_set(s_prnsdk_ctx->set_heart_time);
}

static void prnsdk_check_sleeping_and_wakeup()
{
    do
    {
        uint8_t *addr = NULL;
        uint32_t len = 0 , id = 0 , arg = 0 , data = 0;

        status_manager_get(STATUS_ID_MODULE_POWERMGR , &addr , &len);
        if (len == 0)
        {
            NET_WARN("Cannot get powermanager status.");
            break;
        }
        id = *(uint32_t*)(addr);
        arg = *(uint32_t*)(addr + sizeof(uint32_t));
        pi_free(addr);
        addr = NULL;
        NET_DEBUG("[%x]state parameter value:%x" , id , arg);
        if (id == STATUS_I_POWERMGR_WAKEUP) //就绪状态
        {
            break;
        }
        else if (id == STATUS_I_POWERMGR_SLEEP) //休眠状态
        {
            if (arg == 0x0)//睡眠
            {
                data = 0x3;
            }
            else if (arg == 0x1)//节能
            {
                data = 0x4;
            }
            if (data)
            {
                NET_DEBUG("wakeup system for panel to receive message");
                pi_event_mgr_notify(s_prnsdk_ctx->evt_mgr_cli, EVT_TYPE_POWERMGR_CONFIG, &data, sizeof(data));
            }
        }
        // else wait for next state
        sleep(2);
    }while(1);
}

//return 0 for success, 1 for fail
static int32_t send_msg_to_panel_and_wait(uint32_t msg1, uint32_t msg2, void* msg3, volatile uint32_t* val_ptr, int retry_time, const char* sentinel_str)
{
    uint32_t val = SENTINEL_VAL;
    int32_t status;
    char* p;
    int retry;
    ROUTER_MSG_S  msg;

    prnsdk_check_sleeping_and_wakeup();

    msg.msgType = MSG_PRNSDK_NET_UI_RS;
    msg.msg1 = msg1;
    msg.msg2 = msg2;
    msg.msg3 = msg3;
    msg.msgSender = MID_PORT_NET;

    *val_ptr = SENTINEL_VAL;

    p = strrchr(sentinel_str, '.');
    p = p ? p+1 : (char*)sentinel_str;
    NET_DEBUG("send msg to panel, waitting for panel to return %s", p);
    status = task_msg_send_by_router( MID_PANEL, &msg );

    if ( 0 == status )
    {
        for(retry = 0; (SENTINEL_VAL == *val_ptr) && (retry < retry_time); retry++ )
        {
            sleep(1);
        }
        if (retry >= retry_time)
        {
            NET_ERROR("%s fail to get %s in %d seconds", __func__, p, retry);
        }
    }
    else
    {
        NET_ERROR(" send to MID_PANEL Fail: %d", status);
    }
    return *val_ptr == 0 ? 0 : 1;
}

PRNSDK_REQ_DEFINITION(prnsdk_request_connection)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    char*       json_str = NULL;
    uint32_t    sdk_mode = 0;
    char        random_num[9] = "";
    int         err_code;

    sdk_mode = netdata_get_prnsdk_enabled(DATA_MGR_OF(s_prnsdk_ctx));
    RETURN_VAL_IF(sdk_mode == 0, NET_WARN, -1);

    switch (state_manage_access_get())
    {
        case STATE_TYPE_UNACCESS:
            RETURN_VAL_IF( state_randomnum_get(random_num, sizeof(random_num), 8), NET_WARN, -1 );
            err_code = 0;
            break;
        case STATE_TYPE_ACCESSING:
            err_code = 1;
            break;
        case STATE_TYPE_ACCESSED:
            err_code = 2;
            break;
        default:
            NET_WARN("bug");
            return -1;
    }

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);
    cJSON_AddNumberToObject(root_obj, "ErrorCode", err_code);
    cJSON_AddStringToObject(root_obj, "RandomNum", random_num);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

/**
 * @brief       when heart time is invalid( <=0 or not set), set to default 120s.
 * @return      reply value
 * @retval      == 0      : success\n
 *              == -1    : fail
 */
PRNSDK_REQ_DEFINITION(prnsdk_verify_connection)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str;
    char        printer_name[64] = { 0 };
    char        hostname[128] = {0};
    char        fw_version[64] = { 0 };
    char        serialnum[16] = { 0 };
    int32_t     rc = 0;
    uint32_t    color = 0;
    uint32_t    sdk_mode = 0;
    int         heart_time = DEFAULT_HEART_TIME;
    int         err_code = 1;

    sdk_mode = netdata_get_prnsdk_enabled(DATA_MGR_OF(s_prnsdk_ctx));
    RETURN_VAL_IF(sdk_mode == 0, NET_WARN, -1);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    switch (state_manage_access_get())
    {
        case STATE_TYPE_UNACCESS:
            NET_DEBUG("STATE_TYPE_UNACCESS");
            break;
        case STATE_TYPE_ACCESSING:
            if ( state_printer_add_request(priv->iobuf) )
            {
                NET_DEBUG("state_printer_add_request(pjson) == NULL");
            }
            else
            {
                err_code = 0;

                json_item = cJSON_Parse(priv->iobuf);
                if (json_item != NULL)
                {
                    json_get_str = cJSON_GetObjectItem(json_item, "HeartTime");
                    if (NULL == json_get_str )
                    {
                        NET_WARN("json HeartTime does not exist, use Defaut heart time %d", DEFAULT_HEART_TIME);
                    }
                    else if ( json_get_str->valueint > 0 )
                    {
                        heart_time = json_get_str->valueint;
                    }

                   s_prnsdk_ctx->set_heart_time = heart_time;
                   prnsdk_heart_update();
                   cJSON_Delete(json_item);
                }
            }
            break;
        case STATE_TYPE_ACCESSED:
            NET_DEBUG("STATE_TYPE_ACCESSED");
            break;
        default:
            NET_WARN("bug");
    }

    netdata_get_pdt_name(DATA_MGR_OF(s_prnsdk_ctx), printer_name, sizeof(printer_name));
    netdata_get_fw_ver(DATA_MGR_OF(s_prnsdk_ctx), fw_version, sizeof(fw_version));
    netdata_get_pdt_sn(DATA_MGR_OF(s_prnsdk_ctx), serialnum, sizeof(serialnum));
    netdata_get_hostname(DATA_MGR_OF(s_prnsdk_ctx), hostname, sizeof(hostname));

    color = netdata_get_support_color(DATA_MGR_OF(s_prnsdk_ctx));

    cJSON_AddNumberToObject(root_obj, "ErrorCode", err_code);
    cJSON_AddStringToObject(root_obj, "ProductName", printer_name);
    cJSON_AddStringToObject(root_obj, "FirmwareVersion", fw_version);
    cJSON_AddStringToObject(root_obj, "SDKVersion", get_sdk_version());
    cJSON_AddStringToObject(root_obj, "ProductID", serialnum);
    cJSON_AddStringToObject(root_obj, "Hostname", hostname);
    cJSON_AddNumberToObject(root_obj, "color", color);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_set)
{
    cJSON*        root_obj _autofree_cjson_ = NULL;
    cJSON*        json_item = NULL;
    cJSON*        json_get_str = NULL;
    uint32_t      val;
    int32_t       ret = 1;

    NET_DEBUG("login_ret: -- %u", s_msg_ret_ctx.login_ret);

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    json_item = cJSON_Parse(priv->iobuf);
    if ( json_item != NULL )
    {
        json_get_str = cJSON_GetObjectItem(json_item, "UrlSet");
        if (json_get_str != NULL)
        {
            SET_MUTEX_PROTECT(
                    snprintf(s_prnsdk_ctx->set_url_path, sizeof(s_prnsdk_ctx->set_url_path), "%s", json_get_str->valuestring);
            )
            NET_DEBUG("snprintf url_path %s", s_prnsdk_ctx->set_url_path);
        }
        cJSON_Delete(json_item);

        ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG_PRNSDK_NET_UI_RS_SET_PRINTER, 0, priv->iobuf, &s_msg_ret_ctx.ui_set_ret, 5);
    }

	cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

	*rcontent = cJSON_PrintUnformatted(root_obj);
	return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_login_result)
{
    cJSON*      root_obj  _autofree_cjson_  = NULL;
    cJSON*      json_item _autofree_cjson_  = NULL;
    cJSON*      json_get_str = NULL;
    ROUTER_MSG_S  msg;
    int         err = 1;
    int         login = 0;
    uint32_t    val;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    json_item = cJSON_Parse(priv->iobuf);
    if ( json_item != NULL )
    {
        json_get_str = cJSON_GetObjectItem(json_item, "LoginMode");
        if(json_get_str == NULL)
        {
            NET_DEBUG("sdk LoginMode IS null \n");
            return -1;
        }
        else
        {
            NET_DEBUG("LoginMode %d \n", json_get_str->valueint);
        }

        switch (json_get_str->valueint)
        {
            case 1 :/* 用户名密码登陆 */
                if(state_manage_login_get() == STATE_TYPE_LOGGING_IN)/*登录中*/
                {
                    state_login_result_pro(priv->iobuf);
                    if(state_manage_login_get() == STATE_TYPE_LOGGED_IN)/*登录成功*/
                    {
                        login = 1;
                    }
                }
                else
                {
                    NET_ERROR("state_manage_login_get %d\r\n", state_manage_login_get());
                }
                break;
            case 2 :/* 二维码登陆 */
            case 3 :/* 刷卡登陆 */
                if(state_manage_login_get() == STATE_TYPE_UNLOGIN)/*未登录*/
                {
                    state_login_result_pro(priv->iobuf);
                    if(state_manage_login_get() == STATE_TYPE_LOGGED_IN)/*登录成功*/
                    {
                        login = 1;
                    }
                }
                else
                {
                    NET_ERROR("state_manage_login_get %d", state_manage_login_get());
                }
                break;
        }
        s_msg_ret_ctx.login_ret = login;
        err = login ? 0 : json_get_str->valueint;
    }

    msg.msgType = MSG_PRNSDK_LOGIN_PANEL;
    msg.msg1 = err;
    msg.msg2 = 0;
    msg.msg3 = s_prnsdk_ctx->login_username;
    msg.msgSender = MID_PRNSDK_STATE_MGR;
    task_msg_send_by_router( MID_PANEL, &msg );

    cJSON_AddNumberToObject(root_obj, "ErrorCode", err);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_user_info)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    char        printer_name[64] = { 0 };
    int         rc = 0;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1)
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    netdata_get_pdt_name(DATA_MGR_OF(s_prnsdk_ctx), printer_name, sizeof(printer_name));

    cJSON_AddNumberToObject(root_obj, "ErrorCode", 0);
    cJSON_AddStringToObject(root_obj, "ProductName", printer_name);

    pi_mutex_lock(s_prnsdk_ctx->login_mutex);                                         \
    cJSON_AddStringToObject(root_obj, "UserName", s_prnsdk_ctx->login_username);
    cJSON_AddStringToObject(root_obj, "UserPassword", s_prnsdk_ctx->login_userpassword);
    cJSON_AddStringToObject(root_obj, "DomainName", s_prnsdk_ctx->login_domain);
    memset(s_prnsdk_ctx->login_username, 0x00, sizeof(s_prnsdk_ctx->login_username));
    memset(s_prnsdk_ctx->login_userpassword, 0x00, sizeof(s_prnsdk_ctx->login_userpassword));
    pi_mutex_unlock(s_prnsdk_ctx->login_mutex);                                         \

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_job_info)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    cJSON*      item_obj = NULL;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1)
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    pi_mutex_lock(s_prnsdk_ctx->job_mutex);                                         \
    if (s_prnsdk_ctx->job_update_flag == 1)
    {
        cJSON_AddNumberToObject(root_obj, "ErrorCode", 0);
        s_prnsdk_ctx->job_update_flag = 0;
    }
    else
    {
        cJSON_AddNumberToObject(root_obj, "ErrorCode", 1);
    }

    item_obj = cJSON_Parse(s_prnsdk_ctx->job_printinfo);
    if(item_obj != NULL)
    {
        NET_DEBUG("cJSON_Parse printinfo\n");
        cJSON_AddItemToObject(root_obj, "PrintInfoReport", item_obj);
        //cJSON_Delete(item_obj);
    }
    item_obj = cJSON_Parse(s_prnsdk_ctx->job_scaninfo);
    if(item_obj != NULL)
    {
        NET_DEBUG("cJSON_Parse scaninfo\n");
        cJSON_AddItemToObject(root_obj, "ScanInfoReport", item_obj);
        //cJSON_Delete(item_obj);
    }
    item_obj = cJSON_Parse(s_prnsdk_ctx->job_copyinfo);
    if(item_obj != NULL)
    {
        NET_DEBUG("cJSON_Parse copyinfo\n");
        cJSON_AddItemToObject(root_obj, "CopyInfoReport", item_obj);
        //cJSON_Delete(item_obj);
    }
    memset(s_prnsdk_ctx->job_printinfo, 0x00, sizeof(s_prnsdk_ctx->job_printinfo));
    memset(s_prnsdk_ctx->job_scaninfo, 0x00, sizeof(s_prnsdk_ctx->job_scaninfo));
    memset(s_prnsdk_ctx->job_copyinfo, 0x00, sizeof(s_prnsdk_ctx->job_copyinfo));
    pi_mutex_unlock(s_prnsdk_ctx->job_mutex);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_updated_job_info)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    cJSON*      item_obj = NULL;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1)
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    pi_mutex_lock(s_prnsdk_ctx->job_mutex);                                         \

    item_obj = cJSON_Parse(s_prnsdk_ctx->job_updated_printinfo);
    if(item_obj != NULL)
    {
        NET_DEBUG("cJSON_Parse updated_printinfo");
        cJSON_AddItemToObject(root_obj, "PrintInfoReport", item_obj);
        //cJSON_Delete(item_obj);
    }
    item_obj = cJSON_Parse(s_prnsdk_ctx->job_updated_scaninfo);
    if(item_obj != NULL)
    {
        NET_DEBUG("cJSON_Parse updated_scaninfo");
        cJSON_AddItemToObject(root_obj, "ScanInfoReport", item_obj);
        //cJSON_Delete(item_obj);
    }
    item_obj = cJSON_Parse(s_prnsdk_ctx->job_updated_copyinfo);
    if(item_obj != NULL)
    {
        NET_DEBUG("cJSON_Parse updated_copyinfo");
        cJSON_AddItemToObject(root_obj, "CopyInfoReport", item_obj);
        //cJSON_Delete(item_obj);
    }
    memset(s_prnsdk_ctx->job_updated_printinfo, 0x00, sizeof(s_prnsdk_ctx->job_updated_printinfo));
    memset(s_prnsdk_ctx->job_updated_scaninfo, 0x00, sizeof(s_prnsdk_ctx->job_updated_scaninfo));
    memset(s_prnsdk_ctx->job_updated_copyinfo, 0x00, sizeof(s_prnsdk_ctx->job_updated_copyinfo));
    pi_mutex_unlock(s_prnsdk_ctx->job_mutex);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_capabilities)
{
    ROUTER_MSG_S  msg;
    cJSON*      root_obj _autofree_cjson_ = NULL;
    char*       json_str   = NULL;
    char*       pjson      = NULL;
    int         ret = -1;
    int         i = 0;
    int32_t     status = 0;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_NONE, -1);
    }

    prnsdk_heart_update();
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    pjson = strchr(priv->iobuf, '{');
    if ( pjson == NULL )
    {
        ret = -1;
    }

    if (s_msg_ret_ctx.login_ret == 1)
    {
        ret = authority_adapt_local_data_proc (pjson);
    }
    else
    {
        msg.msgType = MSG_PRNSDK_NET_UI_RS;
        msg.msg1 = 10; /*10: auth job end */
        msg.msg2 = 0;
        msg.msg3 = NULL;
        msg.msgSender = MID_PORT_NET;
        status = task_msg_send_by_router( MID_PANEL, &msg );
        if ( status < 0 )
        {
            NET_ERROR(" send to MID_PANEL Fail: %d", status);
        }
        ret = authority_adapt_recv_data_proc (pjson);

        notify_auth_result(EVENTFD_AUTH_SUCCESS);
    }
    NET_DEBUG("login_ret: -- %u, authority_adapt_recv_data_proc ret: %d", s_msg_ret_ctx.login_ret, ret);

    if ( ret == RET_OK )
    {
        s_msg_ret_ctx.login_ret = 0;
        cJSON_AddNumberToObject(root_obj, "ErrorCode", 0);
        FILE *fp = fopen("/tmp/Capabilities_json_buf", "wb");
        if ( fp == NULL )
        {
            NET_DEBUG("open error!\n");
        }
        else
        {
            fwrite(priv->iobuf, 1, priv->received, fp);
            fclose(fp);
        }
    }
    else {
        cJSON_AddNumberToObject(root_obj, "ErrorCode", 1);
    }

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_job_list)
{
    cJSON*       root_obj _autofree_cjson_ = NULL;
    char*        json_str   = NULL;
    char*        pjson      = NULL;
    int          ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
        RETURN_VAL_IF(state_manage_login_get() != STATE_TYPE_LOGGED_IN, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    if ( priv->received > 500 )
    {
        FILE *fp = fopen("/tmp/job_list_buf", "wb");
        if ( fp == NULL )
        {
           NET_DEBUG("open error!\n");
        }
        else
        {
           fwrite(priv->iobuf, 1, priv->received, fp);
           fclose(fp);
        }
    }

    pjson = strchr(priv->iobuf, '{');

    ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG_PRNSDK_NET_UI_RS_JOB_LIST_UPDATE, 0, pjson, &s_msg_ret_ctx.ui_joblist_ret, 15);
    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_qr_code)
{
    cJSON*      root_obj  _autofree_cjson_ = NULL;
    cJSON*      json_item _autofree_cjson_ = NULL;
    cJSON*      json_get_str;
    char*       pjson = NULL;
    void*       psrc  = NULL;
    int         ret = 1;
    int32_t     base64_ret = 0;
    char        qr_jpeg_path[50] = {0};

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    pjson = strchr(priv->iobuf, '{');
    if ( pjson == NULL )
    {
        NET_ERROR("pjson is NULL!\n");
    }
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    json_item = cJSON_Parse(pjson);
    RETURN_VAL_IF(json_item == NULL, NET_WARN, -1);

    do
    {
        json_get_str = cJSON_GetObjectItem(json_item, "QrCode");
        BREAK_IF((json_get_str == NULL || json_get_str->valuestring == NULL), NET_DEBUG);
        size_t len_qrcode = strlen(json_get_str->valuestring);
        if (len_qrcode >= MAX_HTTP_URL) {
            NET_DEBUG("QrCode %zu over the limit\n", len_qrcode);
            break;
        }
        psrc = pi_zalloc(len_qrcode);
        BREAK_IF(psrc == NULL, NET_WARN);

        base64_ret = base64_decode(json_get_str->valuestring, len_qrcode, psrc, len_qrcode);
        NET_DEBUG("QrCode %d \n", base64_ret);
        if (base64_ret == -1)
        {
            NET_ERROR("base64decode string: %s error", json_get_str->valuestring);
            break;
        }

        struct timeval tv;
        time_t now_time_sec;
        gettimeofday(&tv, NULL);
        now_time_sec = tv.tv_sec;

        struct tm *now_time = localtime(&now_time_sec);
        NET_ERROR("set system time: %04d-%02d-%02d %02d:%02d:%02d\n", (now_time->tm_year+1900), now_time->tm_mon, now_time->tm_mday, now_time->tm_hour, now_time->tm_min, now_time->tm_sec);
        snprintf(qr_jpeg_path, sizeof(qr_jpeg_path), "/tmp/qr_%04d_%02d_%02d_%02d_%02d.jpeg",(now_time->tm_year+1900), now_time->tm_mon, now_time->tm_mday, now_time->tm_hour, now_time->tm_min);

        FILE *fp = fopen(qr_jpeg_path, "wb");
        if (fp == NULL)
        {
            NET_ERROR("open error!\n");
        }
        else
        {
            fwrite(psrc, 1, base64_ret, fp);
            fclose(fp);

            ret= SEND_MSG_TO_PANEL_AND_WAIT(MSG_PRNSDK_NET_UI_RS_QC_UPDATE, 0, qr_jpeg_path, &s_msg_ret_ctx.ui_qr_ret, 5);
        }
    }while(0);

    if (psrc)
    {
        pi_free(psrc);
    }

    cJSON_AddNumberToObject(root_obj, "ErrorCode", (ret == 0) ? 0 : 1);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_exit)
{
    cJSON*      root_obj _autofree_cjson_  = NULL;
    int         ret = -1;
    uint32_t    val;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);
    ret = state_manage_reset();

    SET_MUTEX_PROTECT(
        memset(s_prnsdk_ctx->set_url_path, 0x00, sizeof(s_prnsdk_ctx->set_url_path));
        prnsdk_heart_stop();
    )

    prnsdk_set_enable(0);
    cJSON_AddNumberToObject(root_obj, "ErrorCode", (ret == 0) ? 0 : 1 );

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_heartbeat)
{
    cJSON*      root_obj = NULL;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);
    cJSON_AddNumberToObject(root_obj, "ErrorCode", 0);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

#if CONFIG_NET_SMTP
PRNSDK_REQ_DEFINITION(prnsdk_set_email)
{
     cJSON*       root_obj _autofree_cjson_  = NULL;
     cJSON*       json_item  = NULL;
     char*        pjson      = NULL;
     cJSON*       json_get_str;
     int          ret = 1;

     if (s_msg_ret_ctx.debug_ret != 1)
     {
         RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
         RETURN_VAL_IF(state_manage_login_get() != STATE_TYPE_LOGGED_IN, NET_WARN, -1);
     }
     prnsdk_heart_update();

     RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);
     //NET_DEBUG("m_f_buf:%s -- %d\n", pserver->m_f_buf, pserver->m_f_buf_cnt);
     //NET_DEBUG("m_f_buf: -- %d\n", pserver->m_f_buf_cnt);

     pjson = strchr(priv->iobuf, '{');
     RETURN_VAL_IF(STRING_IS_EMPTY(pjson), NET_INFO, -1);
     NET_DEBUG("pjson: -- %s\n", pjson);

     json_item = cJSON_Parse(pjson);
     if ( json_item != NULL )
     {
            do
            {
                json_get_str = cJSON_GetObjectItem(json_item, "SendMail");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                netctx_update_smtp_sender_addr(s_prnsdk_ctx->net_ctx, json_get_str->valuestring);
                NET_DEBUG("sendmail %s", json_get_str->valuestring);

                json_get_str = cJSON_GetObjectItem(json_item, "SmtpService");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                netctx_update_smtp_server_addr(s_prnsdk_ctx->net_ctx, json_get_str->valuestring);
                NET_DEBUG("smtservice %s", json_get_str->valuestring);

                json_get_str = cJSON_GetObjectItem(json_item, "port");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                netctx_update_smtp_server_port(s_prnsdk_ctx->net_ctx, json_get_str->valueint);
                NET_DEBUG("smtp port %d", json_get_str->valueint);

                json_get_str = cJSON_GetObjectItem(json_item, "EncryptMode");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                NET_DEBUG("EncryptMode %d", json_get_str->valueint);
                netctx_update_smtp_sec_mode(s_prnsdk_ctx->net_ctx, json_get_str->valueint);

                json_get_str = cJSON_GetObjectItem(json_item, "Identity");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                netctx_update_smtp_server_auth(s_prnsdk_ctx->net_ctx, json_get_str->valueint);
                NET_DEBUG("Identity %d", json_get_str->valueint);

                json_get_str = cJSON_GetObjectItem(json_item, "LoginName");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                netctx_update_smtp_username(s_prnsdk_ctx->net_ctx, json_get_str->valuestring);
                NET_DEBUG("LoginName %s", json_get_str->valuestring);

                json_get_str = cJSON_GetObjectItem(json_item, "LoginPassword");
                BREAK_IF(json_get_str==NULL, NET_WARN);
                netctx_update_smtp_password(s_prnsdk_ctx->net_ctx, json_get_str->valuestring);
                NET_DEBUG("LoginPassword %s", json_get_str->valuestring);
                ret = 0;
            }
            while(0);

            cJSON_Delete(json_item);
     }

    cJSON_AddNumberToObject(root_obj, "ErrorCode", (ret == 0) ? 0 : 1);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}
#endif

PRNSDK_REQ_DEFINITION(prnsdk_request_bind_card)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG_PRNSDK_NET_UI_RS_REQUEST_BIND_CARD, 0, NULL, &s_msg_ret_ctx.ui_request_bind_ret, 5);

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_card_user_info)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    //RETURN_VAL_IF_FAIL(state_manage_login_get() == STATE_TYPE_LOGGED_IN, -1)/*如果不是已登录*/

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    cJSON_AddNumberToObject(root_obj, "ErrorCode", 0);

    pi_mutex_lock(s_prnsdk_ctx->login_mutex);
    cJSON_AddStringToObject(root_obj, "CardUserName", s_prnsdk_ctx->login_card_username);
    cJSON_AddStringToObject(root_obj, "CardUserPassword", s_prnsdk_ctx->login_card_userpassword);
    cJSON_AddStringToObject(root_obj, "DomainName", s_prnsdk_ctx->login_card_domain);
    memset(s_prnsdk_ctx->login_card_username, 0x00, sizeof(s_prnsdk_ctx->login_card_username));
    memset(s_prnsdk_ctx->login_card_userpassword, 0x00, sizeof(s_prnsdk_ctx->login_card_userpassword));
    pi_mutex_unlock(s_prnsdk_ctx->login_mutex);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_card_result)
{
    cJSON*      root_obj _autofree_cjson_ = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str = NULL;
    char*       pjson = NULL;
    ROUTER_MSG_S  msg;
    int32_t     status = 0;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    pjson = strchr(priv->iobuf, '{');
    RETURN_VAL_IF(STRING_IS_EMPTY(pjson), NET_INFO, -1);
    NET_DEBUG("pjson: -- %s\n", pjson);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    if( pjson != NULL )
    {
        json_item = cJSON_Parse(pjson);
        if ( json_item != NULL )
        {
            json_get_str = cJSON_GetObjectItem(json_item, "ErrorCode");
            if (json_get_str != NULL)
            {
                ret = 0;
                NET_DEBUG("ErrorCode %d", json_get_str->valueint);
                s_msg_ret_ctx.ui_request_bind_ret = 2;

                msg.msgType = MSG_PRNSDK_NET_UI_RS;
                msg.msg1 = MSG_PRNSDK_NET_UI_RS_BIND_CARD_RESULT;
                msg.msg2 = json_get_str->valueint;
                msg.msg3 = NULL;
                msg.msgSender = MID_PORT_NET;
                status = task_msg_send_by_router( MID_PANEL, &msg );
                if ( 0 > status)
                {
                    NET_ERROR(" send to MID_PANEL Fail  %d\r\n", status);
                    ret = 1;
                }
            }
            cJSON_Delete(json_item);
        }
    }

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_device_status)
{
    cJSON*          root_obj _autofree_cjson_ = NULL;
    cJSON*          json_item = NULL;
    cJSON*          json_get_str = NULL;
    char*           pjson    = NULL;
    int32_t         get_mode = 0;
    uint32_t        remain = 0;
    uint32_t        page_cnt;
    int32_t         ret = 0, len = 0, i = 0;
    PRNSDK_STATUS_S sdk_sts;
    STATIC_STATUS_S static_sts;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    pjson = strchr(priv->iobuf, '{');
    RETURN_VAL_IF(STRING_IS_EMPTY(pjson), NET_INFO, -1);
    NET_DEBUG("pjson: -- %s\n", pjson);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    if( pjson != NULL )
    {
        json_item = cJSON_Parse(pjson);
        if ( json_item != NULL )
        {
            json_get_str = cJSON_GetObjectItem(json_item, "GetMode");
            if (json_get_str != NULL)
            {
                get_mode = json_get_str->valueint;
                NET_DEBUG("GetMode %d", get_mode);
                switch (get_mode)
                {
                    case 1 :     /*获取打印机耗材信息*/
                        remain = netdata_get_tb_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_K);          ///< 黑色 碳粉盒 余量
                        cJSON_AddNumberToObject(root_obj, "A", remain);

#if CONFIG_COLOR //彩机
                        remain = netdata_get_tb_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_Y);          ///< 黄色 碳粉盒 余量
                        cJSON_AddNumberToObject(root_obj, "B", remain);
                        remain = netdata_get_tb_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_C);          ///< 青色 碳粉盒 余量
                        cJSON_AddNumberToObject(root_obj, "C", remain);
                        remain = netdata_get_tb_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_M);          ///< 品红 碳粉盒 余量
                        cJSON_AddNumberToObject(root_obj, "D", remain);
#endif
                        remain = netdata_get_dr_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_K);          ///< 黑色 鼓组件 余量
                        cJSON_AddNumberToObject(root_obj, "E", remain);
#if CONFIG_COLOR //彩机
                        remain = netdata_get_dr_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_Y);          ///< 黄色 鼓组件 余量
                        cJSON_AddNumberToObject(root_obj, "F", remain);

                        remain = netdata_get_dr_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_C);          ///< 青色 鼓组件 余量
                        cJSON_AddNumberToObject(root_obj, "G", remain);

                        remain = netdata_get_dr_remain(DATA_MGR_OF(s_prnsdk_ctx), MARKER_ID_M);          ///< 品红 鼓组件 余量
                        cJSON_AddNumberToObject(root_obj, "H", remain);
#endif

                        //sdk_sts = __atomic_load_n(sizeof(PRNSDK_STATUS_S), &s_sdk_sts, __ATOMIC_RELAXED);
                        __atomic_load(&s_sdk_sts, &sdk_sts, __ATOMIC_RELAXED);
                        cJSON_AddNumberToObject(root_obj, "I", sdk_sts.tray_1);
                        cJSON_AddNumberToObject(root_obj, "J", sdk_sts.tray_2);
                        cJSON_AddNumberToObject(root_obj, "K", sdk_sts.tray_3);
                        cJSON_AddNumberToObject(root_obj, "L", sdk_sts.tray_4);
                        cJSON_AddNumberToObject(root_obj, "M", sdk_sts.tray_lct_in);
                        cJSON_AddNumberToObject(root_obj, "N", sdk_sts.tray_lct_ex);
						break;
					case 2 :     /*获取打印机计数器信息*/
						netdata_get_static_feature(DATA_MGR_OF(s_prnsdk_ctx), &static_sts,  sizeof(static_sts));
						cJSON_AddNumberToObject(root_obj, "PrintPages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_TOTAL));
#if IN_COLOR //彩机
						cJSON_AddNumberToObject(root_obj, "ColourPages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_COLOR));
						cJSON_AddNumberToObject(root_obj, "ColourLessPages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_MONO));
						cJSON_AddNumberToObject(root_obj, "A3Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_COLOR_A3));
						cJSON_AddNumberToObject(root_obj, "A4Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_COLOR_A4));
						cJSON_AddNumberToObject(root_obj, "A5Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_COLOR_A5));
#else
                        cJSON_AddNumberToObject(root_obj, "A3Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_MONO_A3));
                        cJSON_AddNumberToObject(root_obj, "A4Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_MONO_A4));
                        cJSON_AddNumberToObject(root_obj, "A5Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_PRINT_MONO_A5));
#endif
						cJSON_AddNumberToObject(root_obj, "FlatScan", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_SCAN_FB_TO_COPY));
						cJSON_AddNumberToObject(root_obj, "FlatScanHost", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_SCAN_FB_TOTAL));

						//只有ADF机型才显示下面ADF的计数项
						if ( netdata_get_support_scan(DATA_MGR_OF(s_prnsdk_ctx)) > 0 )
						{
							cJSON_AddNumberToObject(root_obj, "AdfScan", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_SCAN_ADF_TO_COPY));
							cJSON_AddNumberToObject(root_obj, "AdScanHost", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_SCAN_ADF_TOTAL));
						}

						cJSON_AddNumberToObject(root_obj, "CopyPages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_TOTAL));
#if IN_COLOR //彩机
						cJSON_AddNumberToObject(root_obj, "CopyA3Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_COLOR_A3));
						cJSON_AddNumberToObject(root_obj, "CopyA4Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_COLOR_A4));
						cJSON_AddNumberToObject(root_obj, "CopyA5Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_COLOR_A5));
#else
						cJSON_AddNumberToObject(root_obj, "CopyA3Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_MONO_A3));
						cJSON_AddNumberToObject(root_obj, "CopyA4Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_MONO_A4));
						cJSON_AddNumberToObject(root_obj, "CopyA5Pages", netdata_get_page_counter(DATA_MGR_OF(s_prnsdk_ctx), PC_IDX_COPY_MONO_A5));
#endif
						break;
					case 3 :     /*获取打印机故障信息*/
                        ret = s_sdk_sts.error_code;
						break;
					case 4 :     /*获取设备状态 就绪，错误，作业中，警告*/
                        cJSON_AddNumberToObject(root_obj, "DeviceStatus", s_sdk_sts.device_sts);
                        break;
					default:
						ret = 1;
						NET_ERROR(" getmode Error");
						break;
				}
			}
			cJSON_Delete(json_item);
		}
	}
	cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

	*rcontent = cJSON_PrintUnformatted(root_obj);
	return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_auth_result)
{
    cJSON*      root_obj = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str = NULL;
    char*       json_str = NULL;
    ROUTER_MSG_S  msg;
    int32_t     status = 0;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    json_item = cJSON_Parse(priv->iobuf);
    if ( json_item != NULL )
    {
        json_get_str = cJSON_GetObjectItem(json_item, "ErrorCode");
        if (json_get_str != NULL)
        {
            ret = 0;
            NET_DEBUG("ErrorCode %d \n", json_get_str->valueint);
            if (json_get_str->valueint == 0) /*远程校验是否成功*/
            {
                NET_DEBUG("sdk_auth_job wait post user");
                notify_auth_result(EVENTFD_AUTH_SUCCESS);
            }
            else
            {
                notify_auth_result(EVENTFD_AUTH_FAIL);
                msg.msgType = MSG_PRNSDK_NET_UI_RS;
                msg.msg1 = 9; /*9: auth job end */
                msg.msg2 = 0;
                msg.msg3 = NULL;
                msg.msgSender = MID_PORT_NET;
                status = task_msg_send_by_router( MID_PANEL, &msg );
                if ( status < 0 )
                {
                    NET_ERROR(" send to MID_PANEL Fail: %d", status);
                }
            }

        }
        cJSON_Delete(json_item);
    }

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_scan_job_list)
{
    cJSON*      root_obj = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str = NULL;
    char*       json_str = NULL;
    char*       pjson = NULL;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_login_get() != STATE_TYPE_LOGGED_IN, NET_WARN, -1);
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    pjson = strchr(priv->iobuf, '{');
    RETURN_VAL_IF(STRING_IS_EMPTY(pjson), NET_INFO, -1);
    NET_DEBUG("pjson: -- %s\n", pjson);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG1_CODE_SCAN_JOB_LIST_UPDATE, 0, pjson, &s_msg_ret_ctx.ui_joblist_ret, 15);

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_copy_job_list_scan)
{
    cJSON*      root_obj = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str = NULL;
    char*       json_str = NULL;
    char*       pjson = NULL;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_login_get() != STATE_TYPE_LOGGED_IN, NET_WARN, -1);
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    pjson = strchr(priv->iobuf, '{');
    RETURN_VAL_IF(STRING_IS_EMPTY(pjson), NET_INFO, -1);
    NET_DEBUG("pjson: -- %s\n", pjson);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG1_CODE_COPY_JOB_WAIT_SCAN_LIST_UPDATE, 0, pjson, &s_msg_ret_ctx.ui_joblist_ret, 15);

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_copy_job_list_print)
{
    cJSON*      root_obj = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str = NULL;
    char*       json_str = NULL;
    char*       pjson = NULL;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_login_get() != STATE_TYPE_LOGGED_IN, NET_WARN, -1);
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    pjson = strchr(priv->iobuf, '{');
    RETURN_VAL_IF(STRING_IS_EMPTY(pjson), NET_INFO, -1);
    NET_DEBUG("pjson: -- %s\n", pjson);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG1_CODE_COPY_JOB_WAIT_PRINT_LIST_UPDATE, 0, pjson, &s_msg_ret_ctx.ui_joblist_ret, 15);

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

PRNSDK_REQ_DEFINITION(prnsdk_update_job_handling)
{
    cJSON*      root_obj = NULL;
    cJSON*      json_item = NULL;
    cJSON*      json_get_str = NULL;
    char*       json_str = NULL;
    char*       pjson = NULL;
    int32_t     status = 0;
    int32_t     ret = 1;

    if (s_msg_ret_ctx.debug_ret != 1)
    {
        RETURN_VAL_IF(state_manage_login_get() != STATE_TYPE_LOGGED_IN, NET_WARN, -1);
        RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, -1);
    }
    prnsdk_heart_update();

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    ret = SEND_MSG_TO_PANEL_AND_WAIT(MSG1_CODE_UPDATE_JOB_HANDLING, 0, NULL, &s_msg_ret_ctx.ui_job_handling_ret, 5);

    cJSON_AddNumberToObject(root_obj, "ErrorCode", ret);

    *rcontent = cJSON_PrintUnformatted(root_obj);
    return 0;
}

static PRNSDK_URL_TABLE_S   s_prnsdk_request_table[] =
{
    { SET_BITS(HTTP_GET),              "/RequestConnection",      prnsdk_request_connection },
    { SET_BITS(HTTP_GET),              "/UserInfo",               prnsdk_user_info},
    { SET_BITS(HTTP_GET),              "/JobInfo",                prnsdk_job_info},
    { SET_BITS(HTTP_GET),              "/JobInfoEx",              prnsdk_updated_job_info},
    { SET_BITS(HTTP_GET),              "/RequestBindCard",        prnsdk_request_bind_card},
    { SET_BITS(HTTP_GET),              "/BindCardInfo",           prnsdk_card_user_info},
    { SET_BITS(HTTP_GET, HTTP_POST),   "/UpdateJobHandling",      prnsdk_update_job_handling},
    { SET_BITS(HTTP_POST),             "/Disconnect",             prnsdk_exit},
    { SET_BITS(HTTP_POST),             "/Heartbeat",              prnsdk_heartbeat},
    { SET_BITS(HTTP_POST),             "/VerifyConnection",       prnsdk_verify_connection },
    { SET_BITS(HTTP_POST),             "/Set",                    prnsdk_set},
    { SET_BITS(HTTP_POST),             "/LoginResult",            prnsdk_login_result},
    { SET_BITS(HTTP_POST),             "/Capabilities",           prnsdk_capabilities},
    { SET_BITS(HTTP_POST),             "/UpdateJobList",          prnsdk_job_list},
    { SET_BITS(HTTP_POST),             "/QrCode",                 prnsdk_qr_code},
#if CONFIG_NET_SMTP
    { SET_BITS(HTTP_POST),             "/SetEmail",               prnsdk_set_email},
#endif
    { SET_BITS(HTTP_POST),             "/BindCardResult",         prnsdk_card_result},
    { SET_BITS(HTTP_POST),             "/DeviceStatus",           prnsdk_device_status},
    { SET_BITS(HTTP_POST),             "/AuthResult",             prnsdk_auth_result},
    { SET_BITS(HTTP_POST),             "/UpdateScanJobList",      prnsdk_scan_job_list},
    { SET_BITS(HTTP_POST),             "/UpdateCopyJobListScan",  prnsdk_copy_job_list_scan},
    { SET_BITS(HTTP_POST),             "/UpdateCopyJobListPrint", prnsdk_copy_job_list_print},
};


/**
 * @brief       Pesf process headers.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   method  : http method.
 * @param[in]   url     : url.
 * @param[in]   version : The HTTP version.
 * @param[in]   content_length : The HTTP content_length.
 * @return      reply value
 * @retval      == 0    : process success\n
 *              <  0    : process fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
static int32_t prnsdk_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV6(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    NET_DEBUG("url(%s)", url);

    if ( content_length != (uint64_t)-1 )
    {
        priv->reqtotal = (size_t)content_length;
    }

    return 0;
}

/**
 * @brief       Pesf process reqbody.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   data    : The body data .
 * @param[in]   ndata   : The  length of body data.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
static int32_t prnsdk_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV6(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        NET_WARN("post body overlength(%zu) from client(%u->%u)\n", ndata, ptask->r_port, ptask->l_port);
        return 0;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;

    return 0;
}

/**
 * @brief       Pesf process request.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   method  : method .
 * @param[in]   url     : http request url .
 * @param[in]   parms   : parms .
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
static int32_t prnsdk_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV6(ptask, PRIV_INFO_S, priv);
    const char* reply_type = APPLICATION_JSON;
    const char* reply_code = "200 OK";
    int32_t     reply_len = 0;
    const char* reply_content = NULL;
    int         rslt;
    size_t      i;
    PRNSDK_URL_TABLE_S *tbl, *end;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(url), NET_WARN, -1);
    priv->iobuf[priv->received] = '\0';

    NET_DEBUG("method(%s) url(%s) parms(%s) body[%zu](%s) from client(%u->%u)", http_method_str(method), url, parms, priv->received, priv->iobuf, ptask->r_port, ptask->l_port);
    tbl = s_prnsdk_request_table;
    end = s_prnsdk_request_table + ARRAY_SIZE(s_prnsdk_request_table);
	for ( ; tbl < end; ++tbl )
	{
		if ( IS_BIT_SET(tbl->request_method_mask, method) && strcmp(tbl->request_url, url + URL_PREFIX_LEN) == 0)
		{
			rslt = tbl->request_handle(priv, &reply_content, &reply_code, &reply_type);
			break;
		}
	}

    RETURN_VAL_IF(tbl == end, NET_WARN, -1);
    RETURN_VAL_IF(rslt == -1, NET_WARN, -1);

    if ( reply_content )
    {
        NET_DEBUG("reply json string(%s)", reply_content);
        reply_len = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", reply_content);
        free((void *)reply_content);
    }

    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        http_task_send(ptask, priv->iobuf, reply_len);
    }

    return 0;
}

static void* thread_heartbeat(void* parm)
{
    uint64_t exp;
    int ret;

    struct pollfd pfds[1];

    pfds[0].fd = s_prnsdk_ctx->heart_timerfd;
    pfds[0].events = POLLIN;

    while(1)
	{
        ret = poll(pfds, ARRAY_SIZE(pfds), -1);
        if (ret <= 0)
        {
            NET_WARN("poll failed with %d", ret);
            continue;
        }
        if (pfds[0].revents & POLLIN)
        {
            read(pfds[0].fd, &exp, sizeof(exp));
            NET_WARN("heartbeart timeout");

            pthread_mutex_lock(&s_prnsdk_ctx->set_mutex);
            memset(s_prnsdk_ctx->set_url_path, 0x00, sizeof(s_prnsdk_ctx->set_url_path));
            state_logout_password();
            state_manage_access_set(STATE_TYPE_UNACCESS);
            prnsdk_heart_stop();
            pthread_mutex_unlock(&s_prnsdk_ctx->set_mutex);
        }
	}
	return NULL;
}

// -2 for other error, -1 for auth fail, 0 for timeout, 1 for auth ok
static int prnsdk_wait_for_auth_result()
{
	struct    pollfd pfds[1];
	eventfd_t val;
    int       ret;

	pfds[0].fd = s_prnsdk_ctx->auth_efd;
	pfds[0].events = POLLIN;

    ret = poll(pfds, ARRAY_SIZE(pfds), PRNSDK_AUTHORIZE_TIMEOUT * 1000);
    RETURN_VAL_IF(ret < 0, NET_ERROR, -2);
    RETURN_VAL_IF(ret == 0, NET_DEBUG, 0);   // timeout

    if (pfds[0].revents & POLLIN)
    {
        if (eventfd_read(s_prnsdk_ctx->auth_efd, &val) == 0)
        {
            if (val == EVENTFD_AUTH_SUCCESS)
            {
                return 1;
            }
            else if (val == EVENTFD_AUTH_FAIL)
            {
                return -1;
            }
        }
    }
    return -2;
}

static void* sdk_event_retry(void *arg)
{
    EVENT_CTX_S* even_data = (EVENT_CTX_S*)arg;
    int          ret, i;

    for (i = 1; i <= 3; i++)
    {
        sleep(10 * i);
        ret = send_event_handle(even_data->request_type, even_data->request_data, 0);
        NET_DEBUG(" sdk_event_retry start(%d) request_type(%s) ret=%d\n", i, even_data->request_type, ret);
        if (ret == 0)
        {
            return NULL;
        }
    }
    return NULL;
}

static int32_t send_event_handle(const char* request_type, const char *request_data, int retry_flag)
{
    struct curl_slist*  headers = NULL;
    cJSON*              root_obj _autofree_cjson_ = NULL;
    char*               json_str = NULL;
    CURL*               curl;
    CURLcode            rc;
    char                serialnum[16] = { 0 };
    int                 is_url_path_empty = 0;
    int                 ret = -1;

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL || request_type == NULL, NET_ERROR, ret);

    NET_DEBUG("request_data(%s) request_type(%s)", request_data, request_type);
    do
    {
        curl = curl_easy_init();
        headers = curl_slist_append(headers, "Content-Type: application/json");

        BREAK_IF(curl == NULL || headers == NULL, NET_WARN);

        SET_MUTEX_PROTECT(
                if (strlen (s_prnsdk_ctx->set_url_path) < 1 )
                {
                    NET_DEBUG(" send_event return");
                    is_url_path_empty = 1;
                }
                else
                {
                    NET_DEBUG(" url_path %s", s_prnsdk_ctx->set_url_path);
                    curl_easy_setopt(curl, CURLOPT_URL, s_prnsdk_ctx->set_url_path);
                }
        )
        RETURN_VAL_IF(is_url_path_empty == 1, NET_WARN, ret);

        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        if (strcmp(request_type, "PrintJob") == 0 || strcmp(request_type, "CopyJobPrint") == 0)
        {
            if( request_data != NULL )
            {
                NET_DEBUG("sdk_event reply data (%s)", request_data);
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, request_data);
            }
        }
        else if (strcmp(request_type, "AuthPrinterUser") == 0)
        {
            if( request_data != NULL )
            {
                NET_DEBUG("sdk_event reply data (%s)", request_data);
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, request_data);
            }
        }
        else if (strcmp(request_type, "JobUploadResult") == 0)
        {
            if( request_data != NULL )
            {
                NET_DEBUG("sdk_event reply data (%s)", request_data);
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, request_data);
            }
        }
        else
        {
            netdata_get_pdt_sn(DATA_MGR_OF(s_prnsdk_ctx), serialnum, sizeof(serialnum));
            cJSON_AddStringToObject(root_obj, "RequestType", request_type);
            cJSON_AddStringToObject(root_obj, "ProductID", serialnum);
            json_str = cJSON_PrintUnformatted(root_obj);
            if (json_str)
            {
                NET_DEBUG("sdk_event reply type(%s)", json_str);
                curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, strlen(json_str));
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_str);
            }
        }

        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5);
        curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);
        //curl_easy_setopt(curl, CURLOPT_FORBID_REUSE, 1);

        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_easy_setopt(curl, CURLOPT_MAXREDIRS, 1);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 3);
        //curl_easy_setopt(curl, CURLOPT_DNS_CACHE_TIMEOUT, 0);

        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        rc = curl_easy_perform(curl);
        if ( rc == CURLE_OK )
        {
            ret = 0;
        }
        else
        {
            NET_ERROR("sdk_event curleasy_perform fail (%s)", curl_easy_strerror(rc));
            if (retry_flag == 1)
            {
                pthread_t thread;
                pthread_attr_t thread_attr;
                EVENT_CTX_S* even_data = malloc(sizeof(EVENT_CTX_S));
                memset(even_data, 0x00, sizeof(EVENT_CTX_S));
                snprintf(even_data->request_type, sizeof(even_data->request_type), request_type);
                if (request_data != NULL )
                {
                    snprintf(even_data->request_data, sizeof(even_data->request_data), request_data);
                }

                pthread_attr_init(&thread_attr);
                pthread_attr_setdetachstate(&thread_attr, PTHREAD_CREATE_DETACHED);

                if ( pthread_create(&thread, &thread_attr, sdk_event_retry, (void *)even_data) )
                {
                    free(even_data);
                    even_data = NULL;
                    NET_ERROR("sdk_event pthread_crate fail~\n");
                }
                pthread_attr_destroy(&thread_attr);
            }
        }
        if (json_str)
        {
            free(json_str);
        }
    }
    while (0);

    if (headers)
    {
        curl_slist_free_all(headers);
    }
    if (curl)
    {
        curl_easy_cleanup(curl);
    }
    return ret;
}

static void save_pull_job_info(SDK_PULL_JOB_INFO_S *pull_job_info, int module)
{
    int32_t  current_job_info_len;
    int32_t  updated_job_info_len;

    RETURN_IF(pull_job_info == NULL, NET_WARN);

    current_job_info_len  = strlen(pull_job_info->sdk_current_job_info);
    updated_job_info_len  = strlen(pull_job_info->sdk_updated_job_info);

    NET_DEBUG("sdk_updated_job_info (%s) (%d) (%d)", pull_job_info->sdk_current_job_info, current_job_info_len, updated_job_info_len);
    if (current_job_info_len > SDK_PULL_JOBINFO_LEN_MAX)
    {
        NET_ERROR("strlen(sdk_current_job_info) > SDK_PULL_JOBINFO_LEN_MAX");
        return;
    }

    pi_mutex_lock(s_prnsdk_ctx->job_mutex);                                         \
    s_prnsdk_ctx->job_update_flag = 1;

    switch (module)
    {
        case MID_PRINT_JOB_MGR:
            memcpy(s_prnsdk_ctx->job_printinfo, pull_job_info->sdk_current_job_info, current_job_info_len);
            NET_DEBUG("memcpy s_prnsdk_ctx->printinfo %s", s_prnsdk_ctx->job_printinfo);
            if (updated_job_info_len > 0)
            {
                memcpy(s_prnsdk_ctx->job_updated_printinfo, pull_job_info->sdk_updated_job_info, updated_job_info_len);
                NET_DEBUG("memcpy s_prnsdk_ctx->updated_printinfo %s", s_prnsdk_ctx->job_updated_printinfo);
            }
            break;
        case MID_SCAN_JOB_MGR:
            memcpy(s_prnsdk_ctx->job_scaninfo, pull_job_info->sdk_current_job_info, current_job_info_len);
            NET_DEBUG("memcpy s_prnsdk_ctx->scaninfo %s", s_prnsdk_ctx->job_scaninfo);
            if (updated_job_info_len > 0)
            {
                memcpy(s_prnsdk_ctx->job_updated_scaninfo, pull_job_info->sdk_updated_job_info, updated_job_info_len);
                NET_DEBUG("memcpy s_prnsdk_ctx->updated_scaninfo %s", s_prnsdk_ctx->job_updated_scaninfo);
            }
            break;
        case MID_COPY_JOB_MGR:
            memcpy(s_prnsdk_ctx->job_copyinfo, pull_job_info->sdk_current_job_info, current_job_info_len);
            NET_DEBUG("memcpy s_prnsdk_ctx->copyinfo %s", s_prnsdk_ctx->job_copyinfo);
            if (updated_job_info_len > 0)
            {
                memcpy(s_prnsdk_ctx->job_updated_copyinfo, pull_job_info->sdk_updated_job_info, updated_job_info_len);
                NET_DEBUG("memcpy s_prnsdk_ctx->updated_copyinfo %s", s_prnsdk_ctx->job_updated_copyinfo);
            }
            break;
        default:
             NET_DEBUG("module no find (%d)", module);
    }
    pi_mutex_unlock(s_prnsdk_ctx->job_mutex);                                         \

}

static void* thread_prnsdk(void* parm)
{
    cJSON*          json_item _autofree_cjson_ = NULL;
    cJSON*          json_get_str;
    cJSON*          root_obj   = NULL;
    char*           json_str   = NULL;
    char*           pjson = NULL;
    char            serialnum[16] = {0};
    ROUTER_MSG_S    rev_msg;
    ROUTER_MSG_S    send_msg;
    int             ret = 0;
    int32_t         status = 0;
    NETSTS_PACKET_S packet;

    RETURN_IF(s_prnsdk_ctx == NULL, NET_WARN);

    netdata_get_pdt_sn(DATA_MGR_OF(s_prnsdk_ctx), serialnum, sizeof(serialnum));
    NET_DEBUG("PrintSystem:%s, serialnum (%s)", get_sdk_version(), serialnum);

    ret = msg_router_register(MID_PRNSDK_NET);
    if ( ret<0 )
    {
       NET_ERROR( "msg_router_register MID_PRNSDK_NET failed");
       return NULL;
    }

    curl_global_init(CURL_GLOBAL_DEFAULT);

    while(1)
    {
        // wait for something to do
        ret = task_msg_wait_forever_by_router(MID_PRNSDK_NET, &rev_msg);
        if (ret < 0)
        {
            NET_ERROR("MID_PRNSDK_NET wait for message failed = %d", ret );
            break;
        }

        switch (rev_msg.msgType)
        {
            case MSG_PRNSDK_NET_UI_RS:
                switch (rev_msg.msg1)
                {
                    case 1:
                        s_msg_ret_ctx.ui_joblist_ret = rev_msg.msg2;
                        NET_DEBUG("panel return ui_joblist_ret: %u", rev_msg.msg2);
                        break;
                    case 2: /* 打印机配置返回 */
                        s_msg_ret_ctx.ui_set_ret = rev_msg.msg2;
                        NET_DEBUG("panel return ui_set_ret: %u", rev_msg.msg2);
                        break;
                    case 3: /* 更新二维码返回 */
                        s_msg_ret_ctx.ui_qr_ret = rev_msg.msg2;
                        NET_DEBUG("panel return ui_qr_ret: %u", rev_msg.msg2);
                        break;
                    case 4: /* 请求绑卡返回 */
                        s_msg_ret_ctx.ui_request_bind_ret = rev_msg.msg2;
                        NET_DEBUG("panel return ui_request_bind_ret: %u\n", rev_msg.msg2);
                        break;
                    case 0:  /* event triggerd by panel */
                        switch (rev_msg.msg2)
                        {
                            case 1: /* 面板触发用户登录 */
                                pjson = (char *)rev_msg.msg3;
                                if ( pjson == NULL )
                                {
                                    NET_ERROR("A NULL login received!");
                                    break;
                                }
                                pi_mutex_lock(s_prnsdk_ctx->login_mutex);
                                do
                                {
                                    ret = -1;
                                    BREAK_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN);
                                    BREAK_IF(state_login_password(STATE_LOGINMETHOD_ACCOUNT, pjson) != 0, NET_WARN);
                                    json_item = cJSON_Parse(pjson);
                                    BREAK_IF(json_item  == NULL, NET_WARN);
                                    memset(s_prnsdk_ctx->login_username, 0x00, sizeof(s_prnsdk_ctx->login_username));
                                    memset(s_prnsdk_ctx->login_userpassword, 0x00, sizeof(s_prnsdk_ctx->login_userpassword));
                                    memset(s_prnsdk_ctx->login_domain, 0x00, sizeof(s_prnsdk_ctx->login_domain));
                                    json_get_str = cJSON_GetObjectItem(json_item, "UserName");

                                    if ( NULL == json_get_str )
                                    {
                                        NET_ERROR("username:NULL \n");
                                        break;
                                    }
                                    else
                                    {
                                        NET_DEBUG("username:%s \n", json_get_str->valuestring);
                                        snprintf(s_prnsdk_ctx->login_username, sizeof(s_prnsdk_ctx->login_username), "%s", json_get_str->valuestring);
                                    }

                                    json_get_str = cJSON_GetObjectItem(json_item, "UserPassword");
                                    if ( NULL == json_get_str )
                                    {
                                        NET_ERROR("username:NULL \n");
                                        memset(s_prnsdk_ctx->login_username, 0x00, sizeof(s_prnsdk_ctx->login_username));
                                        break;
                                    }
                                    else
                                    {
                                        snprintf(s_prnsdk_ctx->login_userpassword, sizeof(s_prnsdk_ctx->login_userpassword), "%s", json_get_str->valuestring);
                                        NET_DEBUG("userpassword:%s\n", json_get_str->valuestring);
                                    }
                                    ret = 0;

                                    json_get_str = cJSON_GetObjectItem(json_item, "domainName");
                                    if ( NULL == json_get_str )
                                    {
                                        NET_ERROR("domainName:NULL \n");
                                        memset(s_prnsdk_ctx->login_domain, 0x00, sizeof(s_prnsdk_ctx->login_domain));
                                        break;
                                    }
                                    else
                                    {
                                        snprintf(s_prnsdk_ctx->login_domain, sizeof(s_prnsdk_ctx->login_domain), "%s", json_get_str->valuestring);
                                        NET_DEBUG("domainName:%s\n", json_get_str->valuestring);
                                    }
                                }while(0);
                                pi_mutex_unlock(s_prnsdk_ctx->login_mutex);
                                if ( ret < 0 || send_event("UserInfo", NULL) )
                                {
                                    send_msg.msgType = MSG_PRNSDK_LOGIN_PANEL;
                                    send_msg.msg1 = -1;
                                    send_msg.msg2 = 0;
                                    send_msg.msg3 = s_prnsdk_ctx->login_username;
                                    send_msg.msgSender = MID_PRNSDK_STATE_MGR;
                                    task_msg_send_by_router( MID_PANEL, &send_msg );
                                }
                                //触发文印服务器更新用户名密码
                                break;
                            case 2: /* 面板触发用户登出 */
                                BREAK_IF(state_logout_password() != 0, NET_WARN);
                                send_event("UserExit", NULL);
                                break;

                            case 3: /* 面板触发作业打印 */
                                pjson = (char *)rev_msg.msg3;
                                if ( pjson == NULL )
                                {
                                    NET_ERROR("A NULL json job received!\r\n");
                                    break;
                                }
                                send_event("PrintJob", pjson);
                                break;
                            case 4: /* 面板触发二维码更新 */
                                send_event("QrCode", NULL);
                                break;
                            case 5: /* 面板触发作业列表更新 */
                                send_event("UpdateJobList", NULL);
                                break;
                            case 6: /* 面板触发更新绑卡用户名密码 */
                                pjson = (char *)rev_msg.msg3;
                                if ( pjson == NULL )
                                {
                                    NET_ERROR("A NULL GQIO received!\r\n");
                                    break;
                                }
                                //NET_DEBUG("pjson %s\n", cJSON_Print((cJSON*)pjson));
                                json_item = cJSON_Parse(pjson);
                                BREAK_IF(json_item  == NULL, NET_WARN);

                                pi_mutex_lock(s_prnsdk_ctx->login_mutex);
                                do
                                {
                                    memset(s_prnsdk_ctx->login_card_username, 0x00, sizeof(s_prnsdk_ctx->login_card_username));
                                    memset(s_prnsdk_ctx->login_card_userpassword, 0x00, sizeof(s_prnsdk_ctx->login_card_userpassword));
                                    memset(s_prnsdk_ctx->login_card_domain, 0x00, sizeof(s_prnsdk_ctx->login_card_domain));
                                    json_get_str = cJSON_GetObjectItem(json_item, "UserName");

                                    if ( NULL == json_get_str )
                                    {
                                        NET_ERROR("username:NULL \n");
                                        break;
                                    }
                                    else
                                    {
                                        NET_DEBUG("username:%s \n", json_get_str->valuestring);
                                        snprintf(s_prnsdk_ctx->login_card_username, sizeof(s_prnsdk_ctx->login_card_username), "%s", json_get_str->valuestring);
                                    }

                                    json_get_str = cJSON_GetObjectItem(json_item, "UserPassword");
                                    if ( NULL == json_get_str )
                                    {
                                        NET_ERROR("userpassword:NULL \n");
                                        break;
                                    }
                                    else
                                    {
                                        snprintf(s_prnsdk_ctx->login_card_userpassword, sizeof(s_prnsdk_ctx->login_card_userpassword), "%s", json_get_str->valuestring);
                                        NET_DEBUG("userpassword:%s\n", json_get_str->valuestring);
                                    }
                                    json_get_str = cJSON_GetObjectItem(json_item, "domainName");
                                    if ( NULL == json_get_str )
                                    {
                                        NET_ERROR("domainname:NULL \n");
                                        break;
                                    }
                                    else
                                    {
                                        snprintf(s_prnsdk_ctx->login_card_domain, sizeof(s_prnsdk_ctx->login_card_domain), "%s", json_get_str->valuestring);
                                        NET_DEBUG("card_Domain:%s\n", json_get_str->valuestring);
                                    }
                                }
                                while(0);
                                pi_mutex_unlock(s_prnsdk_ctx->login_mutex);

                                send_event("BindCard", NULL);
                                //触发文印服务器更新用户名密码
                                break;

                            case 7: /* 面板触发扫描作业列表更新 */
                                send_event("UpdateScanJobList", NULL);
                                break;
                            case 8: /* 面板触发复印待扫描作业列表更新 */
                                send_event("UpdateCopyJobListScan", NULL);
                                break;
                            case 9:
                                send_event("UpdateCopyJobListPrint", NULL);
                                break;
                            case 10: /* 面板触发复印作业打印 */
                                pjson = (char *)rev_msg.msg3;
                                if ( NULL == pjson )
                                {
                                    NET_ERROR("A NULL CopyPrintJob received!");
                                    break;
                                }
                                send_event("CopyJobPrint", pjson);
                                break;
                            case 11: /* 面板触发作业删除 */
                                pjson = (char *)rev_msg.msg3;
                                if ( NULL == pjson )
                                {
                                    NET_ERROR("A NULL DeleteJob received!");
                                    break;
                                }
                                send_event("DeleteJob", pjson);
                                break;
                        }
                        break;
                }
                break;

            case MSG_PRNSDK_JOB_INFO:
                if (rev_msg.msg3 == NULL)
                {
                    NET_ERROR("rev_msg.msg3!");
                    break;
                }
                save_pull_job_info(rev_msg.msg3, rev_msg.msgSender);
                NET_DEBUG("event *****JobInfo****");
                send_event_handle("JobInfo", NULL, 1);
                break;

            case MSG_PRNSDK_JOB_UPLOAD_INFO:                        /* 留底作业状态更新 */
                if (rev_msg.msg3 == NULL)
                {
                    NET_ERROR("MSG_SDK_JOB_UPLOAD_INFO rev_msg.msg3 NULL");
                    break;
                }
                if (strlen(rev_msg.msg3) > SDK_PULL_JOBINFO_LEN_MAX)
                {
                    NET_ERROR("strlen(rev_msg.msg3) > SDK_PULL_JOBINFO_LEN_MAX");
                    break;
                }
                NET_DEBUG("MSG_SDK_JOB_UPLOAD_INFO %s %d", (char*)rev_msg.msg3, strlen(rev_msg.msg3));

                pi_mutex_lock(s_prnsdk_ctx->job_mutex);
                snprintf(s_prnsdk_ctx->job_upload_info, sizeof(s_prnsdk_ctx->job_upload_info), "%s", (char*)rev_msg.msg3);
                pi_mutex_unlock(s_prnsdk_ctx->job_mutex);

                send_event("JobUploadResult", rev_msg.msg3);
                break;

            default:
                NET_ERROR(" msgType NET_ERROR[%u]", rev_msg.msgType);
                break;
        }
        if ( rev_msg.msg3 )
        {
            free(rev_msg.msg3);
        }
    }
    return NULL;
}

int32_t prnsdk_construct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[6] == NULL )
    {
        ptask->priv_subclass[6] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[6] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = prnsdk_process_headers;
    ptask->reqbody_received_callback = prnsdk_process_reqbody;
    ptask->request_complete_callback = prnsdk_process_request;

    return 0;
}

void prnsdk_destruct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    if ( ptask != NULL && ptask->priv_subclass[6] != NULL )
    {
        pi_free(ptask->priv_subclass[6]);
        ptask->priv_subclass[6] = NULL;
    }
}

static void* prnsdk_update_sysstat_thread(void* parm)
{
    NETSTS_PACKET_S  packet  = { .count = 0 };
    PRNSDK_STATUS_S  sdk_sts;
    uint32_t status_id, status_id_type, error_cdoe;
    uint32_t error_code = 0;

    memset(&sdk_sts, 0, sizeof(sdk_sts));

    RETURN_IF (netsts_take_packet(&packet) != 0, NET_WARN);
    for ( size_t i = 0; i < packet.count; ++i )
    {
        status_id = packet.array[i].status_id;
        status_id_type = status_id & STATUS_ID_TYPE_MASK;

        if ( status_id_type >= STATUS_ID_TYPE_WARN )
        {
            if (sdk_sts.device_sts != DEVICE_STATUS_ERROR)
            {
                sdk_sts.device_sts = status_id_type == STATUS_ID_TYPE_WARN ? DEVICE_STATUS_WARN : DEVICE_STATUS_ERROR;
                error_code = syssts_map_prnsts(status_id);
                if (error_code == 0)
                {
                    NET_WARN("system status id %u counld not map to device error code", status_id);
                }
            }
            else   // have encountered error before
            {
                if (error_code == 0) // FIXME: should not happen
                {
                    error_code = syssts_map_prnsts(status_id);
                }
            }
        }
        else
        {
            if (status_id == STATUS_I_SCAN_RUNNING ||
                    status_id == STATUS_I_SCAN_PROCESSING ||
                    status_id == STATUS_I_PRINT_PRINTING  ||
                    status_id == STATUS_I_SCAN_PROCESSING )
            {
                sdk_sts.device_sts = DEVICE_STATUS_RUNNING;
            }
        }

        switch (status_id)
        {
            case STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY :
                sdk_sts.tray_1 = 1;
                break;
            case STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY :
                sdk_sts.tray_2 = 1;
                break;
            case STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY :
                sdk_sts.tray_3 = 1;
                break;
            case STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY :
                sdk_sts.tray_4 = 1;
                break;
            case STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY :
                sdk_sts.tray_lct_in = 1;
                break;
            case STATUS_W_PRINT_LCT_EX_PAPER_NEAR_EMPTY :
                sdk_sts.tray_lct_ex = 1;
                break;

            case STATUS_E_PRINT_TRAY_1_PAPER_EMPTY :
            case STATUS_W_PRINT_TRAY_1_PAPER_EMPTY :
                sdk_sts.tray_1 = 2;
                break;
            case STATUS_E_PRINT_TRAY_2_PAPER_EMPTY :
            case STATUS_W_PRINT_TRAY_2_PAPER_EMPTY :
                sdk_sts.tray_2 = 2;
                break;
            case STATUS_E_PRINT_TRAY_3_PAPER_EMPTY :
            case STATUS_W_PRINT_TRAY_3_PAPER_EMPTY :
                sdk_sts.tray_3 = 2;
                break;
            case STATUS_E_PRINT_TRAY_4_PAPER_EMPTY :
            case STATUS_W_PRINT_TRAY_4_PAPER_EMPTY :
                sdk_sts.tray_4 = 2;
                break;
            case STATUS_E_PRINT_LCT_IN_PAPER_EMPTY :
            case STATUS_W_PRINT_LCT_IN_PAPER_EMPTY :
                sdk_sts.tray_lct_in = 2;
                break;
            case STATUS_E_PRINT_LCT_EX_PAPER_EMPTY :
            case STATUS_W_PRINT_LCT_EX_PAPER_EMPTY :
                sdk_sts.tray_lct_ex = 2;
                break;

            case STATUS_E_PRINT_TRAY_1_OPEN :
            case STATUS_W_PRINT_TRAY_1_OPEN :
                sdk_sts.tray_1 = 3;
                break;
            case STATUS_E_PRINT_TRAY_2_OPEN :
            case STATUS_W_PRINT_TRAY_2_OPEN :
                sdk_sts.tray_2 = 3;
                break;
            case STATUS_E_PRINT_TRAY_3_OPEN :
            case STATUS_W_PRINT_TRAY_3_OPEN :
                sdk_sts.tray_3 = 3;
                break;
            case STATUS_E_PRINT_TRAY_4_OPEN :
            case STATUS_W_PRINT_TRAY_4_OPEN :
                sdk_sts.tray_4 = 3;
                break;
            case STATUS_E_PRINT_LCT_IN_OPEN :
            case STATUS_W_PRINT_LCT_IN_OPEN :
                sdk_sts.tray_lct_in = 3;
                break;
            case STATUS_E_PRINT_LCT_EX_OPEN :
                sdk_sts.tray_lct_ex = 3;
                break;
        }
    }
    sdk_sts.error_code = error_code;
	__atomic_store(&s_sdk_sts, &sdk_sts, __ATOMIC_RELAXED);

    if (sdk_sts.device_sts == DEVICE_STATUS_ERROR)
    {
        send_event("PrinterError", NULL);
    }
}

static void prnsdk_update_sysstat_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    pthread_t pid;
    pthread_attr_t attr;

    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_prnsdk_ctx->prnsdk_enabled == 0, NET_DEBUG);

    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    if (pthread_create(&pid, &attr, prnsdk_update_sysstat_thread, NULL) != 0)
    {
        NET_WARN("create thread prnsdk_update_sysstat_thread failed: %s", strerror(errno));
    }
    pthread_attr_destroy(&attr);
}

static void prnsdk_event_callback(const EVT_MSG_S* msg, void* ctx)
{
    uint32_t module_id = msg->module_id;
    uint32_t event_type = msg->event_type;
    uint32_t enable;

    pi_log_d( "get module: %u request event type: %u\n", msg->module_id, msg->event_type);

    switch (event_type)
    {
        case EVT_TYPE_PRNSDK_ENABLE_REQUEST:
            enable = *(uint32_t*)msg->data;
            if (enable == 0)
            {
                state_manage_reset();
            }
            prnsdk_set_enable(enable);
            break;
        default:
            break;
    }
}

int prnsdk_event_register()
{
    EVT_MGR_CLI_S * cli_ptr = pi_event_mgr_create_client(EVT_MODULE_PRNSDK, prnsdk_event_callback, NULL, NULL);

    RETURN_VAL_IF (cli_ptr == NULL, NET_WARN, -1);

    uint32_t event_array[] = {
        EVT_TYPE_PRNSDK_ENABLE_REQUEST,
    };

    pi_nvram_get(PRNSDK_ID_PRNSDK_ENABLE, VTYPE_UINT, (void*)&s_prnsdk_ctx->prnsdk_enabled, sizeof(s_prnsdk_ctx->prnsdk_enabled));
    pi_event_mgr_notify(cli_ptr, EVT_TYPE_PRNSDK_ENABLE_MODIFY, (void *)&s_prnsdk_ctx->prnsdk_enabled, sizeof(s_prnsdk_ctx->prnsdk_enabled));
    s_prnsdk_ctx->evt_mgr_cli = cli_ptr;

    return pi_event_mgr_register(cli_ptr, event_array, ARRAY_SIZE(event_array));
}

int32_t prnsdk_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_prnsdk_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_prnsdk_ctx = (PRNSDK_CTX_S*)pi_zalloc(sizeof(PRNSDK_CTX_S));
    RETURN_VAL_IF(s_prnsdk_ctx == NULL, NET_WARN, -1);

	do {
		s_prnsdk_ctx->net_ctx = net_ctx;
		s_prnsdk_ctx->tid_prnsdk = pi_thread_create(thread_prnsdk, PI_LARGE_STACK, NULL, 15, NULL, "Print SDK Thread");
		BREAK_IF(s_prnsdk_ctx->tid_prnsdk == INVALIDTHREAD, NET_WARN);

        s_prnsdk_ctx->heart_timerfd = timerfd_create(CLOCK_MONOTONIC, TFD_NONBLOCK | TFD_CLOEXEC);
        BREAK_IF (s_prnsdk_ctx->heart_timerfd < 0, NET_WARN);

		s_prnsdk_ctx->tid_heartbeat = pi_thread_create(thread_heartbeat, PI_LARGE_STACK, NULL, 15, NULL, "Print SDK Heartbeat Thread");
		BREAK_IF(s_prnsdk_ctx->tid_heartbeat == INVALIDTHREAD, NET_WARN);

		s_prnsdk_ctx->login_mutex     = pi_mutex_create();
		s_prnsdk_ctx->job_mutex       = pi_mutex_create();
		s_prnsdk_ctx->auth_mutex      = pi_mutex_create();

		BREAK_IF((s_prnsdk_ctx->login_mutex == INVALIDMTX) ||
		         (s_prnsdk_ctx->job_mutex   == INVALIDMTX) ||
		         (s_prnsdk_ctx->auth_mutex== INVALIDMTX), NET_WARN );

		pthread_mutex_init(&s_prnsdk_ctx->set_mutex, NULL);
		BREAK_IF(pthread_mutex_init(&s_prnsdk_ctx->set_mutex, NULL), NET_WARN);

        s_prnsdk_ctx->auth_efd = eventfd(0, EFD_NONBLOCK);
        BREAK_IF(s_prnsdk_ctx->auth_efd == -1, NET_WARN);

		s_prnsdk_ctx->net_ctx = net_ctx;
        netctx_add_sysstat_observer(s_prnsdk_ctx->net_ctx, prnsdk_update_sysstat_callback, NULL);
        ret = prnsdk_event_register();
        if ( ret < 0 )
        {
            NET_DEBUG("register event failed(%d)", ret);
            break;
        }

		ret = 0;
	} while ( 0 );

    NET_INFO("prnsdk initialize result(%d)", ret);
    if ( ret != 0 )
    {
        prnsdk_epilog();
    }
    return ret;
}

void prnsdk_epilog(void)
{
    if ( s_prnsdk_ctx != NULL )
    {
		if ( s_prnsdk_ctx->login_mutex )
		{
			pi_mutex_destroy(s_prnsdk_ctx->login_mutex);
		}
		if ( s_prnsdk_ctx->job_mutex )
		{
			pi_mutex_destroy(s_prnsdk_ctx->job_mutex);
		}
		if ( s_prnsdk_ctx->auth_mutex)
		{
			pi_mutex_destroy(s_prnsdk_ctx->auth_mutex);
		}
		pthread_mutex_destroy(&s_prnsdk_ctx->set_mutex);

        if ( s_prnsdk_ctx->tid_prnsdk != INVALIDTHREAD )
        {
            pi_thread_destroy(s_prnsdk_ctx->tid_prnsdk);
        }
        if ( s_prnsdk_ctx->tid_heartbeat != INVALIDTHREAD )
        {
            pi_thread_destroy(s_prnsdk_ctx->tid_heartbeat);
        }
        if (s_prnsdk_ctx->auth_efd >= 0)
        {
            pi_close(s_prnsdk_ctx->auth_efd);
        }
        if (s_prnsdk_ctx->heart_timerfd > 0)
        {
            close(s_prnsdk_ctx->heart_timerfd);
        }
        if (s_prnsdk_ctx->evt_mgr_cli)
        {
            pi_event_mgr_destroy_client(s_prnsdk_ctx->evt_mgr_cli);
        }

		pi_free(s_prnsdk_ctx);
		s_prnsdk_ctx = NULL;
	}
}

/**
 * @brief          Read REMOTE_PRINT_PREPARSER_LEN bytes from qio, and check if is remote print
 * @param[in]      qio     : The qio to be parsed
 * @param[in][out  buf_len : The job timeout seconds.
 * @return         1 if is valid remote print
 *                 0 if invalid paramter or not remote print
 *                 -1 if is remote print but not valid
 * <AUTHOR> JiaMing
 * @date           2025-4-25
 */
int prnsdk_check_remote_print(const char* buf, int32_t buf_len)
{
    int ret;
    char login_name[256] = { 0 };
    char auth_username[256];
    char auth_userpsswd[256];
    char auth_domain[256];
    char auth_jobowner[64];
    char serialnum[16] = {0};
    char* json_str;
    int  lock_ok = 0;
    cJSON* root_obj;

    RETURN_VAL_IF(buf == NULL || buf_len <=0, NET_WARN, 0);

    ret = prnsdk_get_info( (uint8_t*)buf, buf_len,
            auth_domain,   sizeof(auth_domain),
            auth_username, sizeof(auth_username),
            auth_userpsswd, sizeof(auth_userpsswd),
            auth_jobowner, sizeof(auth_jobowner) );

    // ret!=0 not remote print
    RETURN_VAL_IF(ret, NET_WARN, 0);

    RETURN_VAL_IF(state_manage_access_get() != STATE_TYPE_ACCESSED, NET_WARN, 0);

    netdata_get_pdt_sn(DATA_MGR_OF(s_prnsdk_ctx), serialnum, sizeof(serialnum));
    root_obj = cJSON_CreateObject();
    RETURN_VAL_IF(root_obj == NULL, NET_ERROR, 0);

    cJSON_AddStringToObject(root_obj, "RequestType", "AuthPrinterUser");
    cJSON_AddStringToObject(root_obj, "AuthUserName", auth_username);
    cJSON_AddStringToObject(root_obj, "AuthUserPassword", auth_userpsswd);
    cJSON_AddStringToObject(root_obj, "DomainName", auth_domain);
    cJSON_AddStringToObject(root_obj, "ProductID", serialnum);
    authority_adapt_get_usrname(login_name, sizeof(login_name));
    NET_DEBUG(" login name (%s), auth username (%s), auth_jobowner (%s)", login_name, auth_username, auth_jobowner);
    json_str = cJSON_PrintUnformatted(root_obj);
    cJSON_Delete(root_obj);
    RETURN_VAL_IF(json_str == NULL, NET_DEBUG, -1);

    lock_ok = pi_mutex_timedlock(s_prnsdk_ctx->auth_mutex, PRNSDK_AUTHORIZE_TIMEOUT, 0) == 0 ? 1 : 0;

    ret = -1;
    if (lock_ok)
    {
        if ( send_event("AuthPrinterUser", json_str) == 0 )
        {
            if ( prnsdk_wait_for_auth_result() == 1 )
            {
                ret = 1;   // valid remote print
            }
        }
        pi_mutex_unlock(s_prnsdk_ctx->auth_mutex);
    }
    free(json_str);
    return ret;
}

/**
 *@}
 */

