/**************************************************************
Copyright (c) 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:    prnsdk
file name:      prnsdk_authz.c
author:         <PERSON>(<EMAIL>)
date:           2024-11-26
description:    prnsdk parse qio header
**************************************************************/

#define READ_PARSER_LEN     1024
#define LINE_PARSER_SIZE    256

#define PJL_SDK_DOMAIN   "@PJL SDKDOMAINNAME"
#define PJL_SDK_USER     "@PJL SET SDKUSERNAME"
#define PJL_SDK_PSSWD    "@PJL SDKUSERPASSWORD"
#define PJL_SDK_JOBOWNER "@PJL SET JOBOWNER"

#include <stdint.h>
#include <string.h>
#include "pol/pol_define.h"
#include "nettypes.h"

typedef enum
{
    DMAN_NAME_ITEM = 0,
    USER_NAME_ITEM,
    PASS_WORD_ITEM,
    JOB_OWNER_ITEM,
}SDK_AUTH_ITEM;

#define AUTH_ITEMS_MASK  (1 << DMAN_NAME_ITEM | 1 << USER_NAME_ITEM | 1 << PASS_WORD_ITEM | 1 << JOB_OWNER_ITEM)

// return 0 if success, -1 for fail
static int parse_one_line( char* line, char** out_item_name,  char* item_val, int val_len )
{
    int i;
    char* p, *delim, *item_name;

    RETURN_VAL_IF(line == NULL, NET_WARN, -1);

    delim = strchr(line, '=');
    RETURN_VAL_IF( !delim, NET_WARN, -1);

    *delim = '\0';
    item_name = line;

    //strip blank before and after
    for (item_name = line; item_name < delim && *item_name == ' '; ++item_name );
    for (p = delim - 1; p >= item_name && *p == ' '; --p)
    {
        *p = '\0';
    }

    p = strchr(delim+1, '"');
    RETURN_VAL_IF(!p, NET_WARN, -1);

    for (++p, i=0; (*p) && (*p) !='"' && i < (val_len-1); ++p, ++i)
    {
        item_val[i] = *p;
    }
    if (*p == '"')
    {
        item_val[i] = '\0';
    }
    else
    {
        NET_ERROR("can not find end \"");
        return -1;
    }

    *out_item_name = item_name;
    return 0;
}
/*******************************************************************************
 Description    : get the domain/user/password from qio stream data
 output parm    : auth_domain -- domain string buf to which the domain string put
                  auth_user -- user string buf to which the username put
                  auth_psswd -- password string buf to which the password put
 Return Parm    : 0 -- success          -1 -- fail
*******************************************************************************/
int prnsdk_get_info(unsigned char* buf, int32_t buf_len,
        char* auth_domain, size_t domain_len,
        char* auth_user,   size_t user_len,
        char* auth_psswd,  size_t psswd_len,
        char* auth_owner,  size_t owner_len)
{
    int i, j, k;
    int end_line = 0;
    uint8_t byte_data;
    int ret = 0;
    char* item_name;
    char item_val[LINE_PARSER_SIZE];

    uint8_t line_data[LINE_PARSER_SIZE] = {0};

    struct {
        int offset;
        const char* name;
        char* stor_buf;
        uint32_t buf_len;
    } auth_items[] =
    {
        { DMAN_NAME_ITEM, PJL_SDK_DOMAIN,   auth_domain, domain_len},
        { USER_NAME_ITEM, PJL_SDK_USER  ,   auth_user, user_len},
        { PASS_WORD_ITEM, PJL_SDK_PSSWD ,   auth_psswd, psswd_len},
        { JOB_OWNER_ITEM, PJL_SDK_JOBOWNER, auth_owner, owner_len},
    };
    int auth_tag = 0;

    RETURN_VAL_IF(buf_len > READ_PARSER_LEN, NET_ERROR, 0);

    for(i = 0, j = 0; i < buf_len; i++)
    {
        /* read the charactor one by one and 0x0d 0x0a is the end of the line */
#if XOR_ONOFF
        byte_data = (*(buf+i)) ^ XOR_BYTE;
#else
        byte_data = (*(buf+i));
#endif
        if ((0xd == byte_data)||(0xa == byte_data))
        {
            end_line = 1;   // one line
        }
        else
        {
            /* not found end charactor, continue to read next charactor */
            if (j < LINE_PARSER_SIZE)
            {
                line_data[j] = byte_data;
                j++;
            }
        }

        /* begin to proc the second end flag 0x0d 0x0a */
        if (end_line)
        {
            end_line = 0;

            /*another end flag charactor*/
            if (0 == j)
            {
                continue;
            }

            /*set the end of the string*/
            line_data[MIN(j, LINE_PARSER_SIZE-1)] = '\0';

            NET_DEBUG("Read line data is <%s>", (char*)line_data);

            j = 0;
        }
        else
        {
            continue;
        }

        if (parse_one_line((char*)line_data, &item_name, item_val, sizeof(item_val)))
        {
            continue;
        }

        for (k = 0; k < ARRAY_SIZE(auth_items); ++k)
        {
            if ( ((auth_tag & (1 << auth_items[k].offset)) == 0) &&
                 strcmp(item_name, auth_items[k].name) == 0 )
            {
                snprintf(auth_items[k].stor_buf, auth_items[k].buf_len, "%s", item_val);
                auth_tag |= (1 << auth_items[k].offset);
            }
        }
        BREAK_IF (auth_tag == AUTH_ITEMS_MASK, NET_NONE);
    }

    if (auth_tag == AUTH_ITEMS_MASK)
    {
        return 0;
    }
    return -1;
}

