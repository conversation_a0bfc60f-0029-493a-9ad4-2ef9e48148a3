/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file prnsdk_state_manager.c
 * @addtogroup mainapp
 * @{
 * @brief PRINT SDK state manager module
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-09
 */
#include <stdio.h>
#include <time.h>
#include <pthread.h>
#include "utilities/msgrouter.h"
#include "cjson/cJSON.h"
#include "pol/pol_io.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"
#include "pol/pol_types.h"
#include "pol/pol_string.h"
#include "msgrouter_main.h"
#include "prnsdk_state_timer.h"
#include "prnsdk_state_manage/prnsdk_state_manage.h"
#include "license_authentication/license_auth_api.h"
#include "authority_adapt/authority_adapt.h"

#define PRNSDK_VERSION_STRING   "1.0.1"
#define STATE_RET_OK            0
#define STATE_RET_FAIL          -1

static pthread_spinlock_t   spin_user_id        = 0;
static STATE_MANAGE_S       m_state_manage      = {0};
static STATE_TIMER_S        state_access_timer  = {0};
static STATE_TIMER_S        state_login_timer   = {0};
static uint32_t             m_user_id           = 0;

char * get_sdk_version( void )
{
    return PRNSDK_VERSION_STRING;
}

STATE_ACCESS_TYPE_E state_manage_access_get ( void )
{
    pthread_spin_lock(&m_state_manage.spin_access);
    STATE_ACCESS_TYPE_E state_manage = m_state_manage.state_access;
    pthread_spin_unlock(&m_state_manage.spin_access);

    return state_manage;
}

void state_manage_access_set (STATE_ACCESS_TYPE_E state_access)
{
    int32_t status = 0;
    ROUTER_MSG_S sendMsg;

    if ( state_access == state_manage_access_get() )
    {
        pi_log_d(" Current access state already set  %d,  set\n", state_access);
        return ;
    }
    pthread_spin_lock(&m_state_manage.spin_access);
    m_state_manage.state_access = state_access;
    pi_log_d("access state change to %d\n", state_access);
    pthread_spin_unlock(&m_state_manage.spin_access);

    //sendMsg 通知面板
    sendMsg.msgType     = MSG_PRNSDK_ACCESS_PANEL;
    sendMsg.msg1        = state_access;
    sendMsg.msg2        = 0;
    sendMsg.msg3        = NULL;
    sendMsg.msgSender   = MID_PRNSDK_STATE_MGR;
    status = task_msg_send_by_router( MID_PANEL, &sendMsg );
    if ( 0 != status )
    {
        pi_log_e(" send MSG_PRNSDK_ACCESS_PANEL Fail %d\n", status);
    }

}

STATE_LOGIN_TYPE_E state_manage_login_get ( void )
{
    pthread_spin_lock(&m_state_manage.spin_login);
    STATE_LOGIN_TYPE_E state_login = m_state_manage.state_login;
    pthread_spin_unlock(&m_state_manage.spin_login);

    return state_login;
}

void state_manage_login_set (STATE_LOGIN_TYPE_E state_login)
{
    if ( state_login == state_manage_login_get() )
    {
        pi_log_d(" Current login state already set  %d,  set\n", state_login);
        return;
    }
    pthread_spin_lock(&m_state_manage.spin_login);
    m_state_manage.state_login = state_login;
    pi_log_d("login state change to %d\n", state_login);
    pthread_spin_unlock(&m_state_manage.spin_login);

}

STATE_AUTHORIZATION_TYPE_E state_manage_authorization_get ( void )
{
    pthread_spin_lock(&m_state_manage.spin_authorization);
    STATE_AUTHORIZATION_TYPE_E state_authorization = m_state_manage.state_authorization;
    pthread_spin_unlock(&m_state_manage.spin_authorization);

    return state_authorization;
}

void state_manage_authorization_set (STATE_AUTHORIZATION_TYPE_E state_authorization)
{
    if ( state_authorization == state_manage_authorization_get() )
    {
        return ;
    }
    pthread_spin_lock(&m_state_manage.spin_authorization);
    m_state_manage.state_authorization = state_authorization;
    pi_log_d("authorization state change to %d\n", state_authorization);
    pthread_spin_unlock(&m_state_manage.spin_authorization);

}

MANAGE_CONTROL_MODE_TYPE_E manage_control_model_get ( void )
{
    pthread_spin_lock(&m_state_manage.spin_control_mode);
    MANAGE_CONTROL_MODE_TYPE_E manage_control_mode = m_state_manage.manage_control_mode;
    pthread_spin_unlock(&m_state_manage.spin_control_mode);

    return manage_control_mode;
}

void manage_control_model_set (MANAGE_CONTROL_MODE_TYPE_E manage_control_mode)
{
    if ( manage_control_mode == manage_control_model_get() )
    {
        return ;
    }
    pthread_spin_lock(&m_state_manage.spin_control_mode);
    m_state_manage.manage_control_mode = manage_control_mode;
    pi_log_d("control_mode change to %d\n", manage_control_mode);
    pthread_spin_unlock(&m_state_manage.spin_control_mode);

}

int32_t manage_control_mutex_init ( void )
{
    int32_t ret;

    ret = (pthread_spin_init(&m_state_manage.spin_access, 0) ||
            pthread_spin_init(&m_state_manage.spin_login, 0) ||
            pthread_spin_init(&m_state_manage.spin_authorization, 0) ||
            pthread_spin_init(&m_state_manage.spin_control_mode, 0) ||
             pthread_spin_init(&m_state_manage.spin_user_id, 0) );
    if ( 0 == ret )
    {
        pi_log_e("pthread_spin_init failed (code:%d)\n", ret);

        return STATE_RET_FAIL;
    }
    return STATE_RET_OK;
}

static void access_timeout_callback (void *data)
{
    pi_log_d("access_timeout_callback\n");
    //接入超时,接入状态设置为未接入
    state_manage_access_set(STATE_TYPE_UNACCESS);
    //销毁定时器
    state_timer_destroy(&state_access_timer);

}

int32_t state_randomnum_get(char *buffer, size_t buffer_size, int32_t length)
{
    //设置定时器的超时时长2400 1650 2250
    struct timeval timeout_arg = {
        .tv_sec  = 120,
        .tv_usec = 0,
    };

    if ( -1 == state_timer_init(&state_access_timer, access_timeout_callback, NULL, timeout_arg, 0) )
    {
        pi_log_e("state_timer_init error,return! \n");
        return STATE_RET_FAIL;
    }

    state_timer_start(&state_access_timer);
    //状态改为接入中
    state_manage_access_set(STATE_TYPE_ACCESSING);
    if ( -1 == license_auth_generate_random_number(buffer, buffer_size, length) )
    {
        //销毁定时器
        if ( -1 == state_timer_destroy(&state_access_timer) )
        {
            pi_log_e("state_timer_destroy error! \n");
        }
        pi_log_e("license_auth_generate_random_number error! \n");
        return STATE_RET_FAIL;
    }
    pi_log_d("license_auth_generate_random_number ok {%s}\n", buffer);

    return STATE_RET_OK;
}

int32_t state_printer_add_request (char *buf)
{
    //>Access_Authentication->adk_authentication
    int32_t ret = license_auth_access_authentication(buf);

    if ( 0 == ret )
    {
        state_manage_access_set(STATE_TYPE_ACCESSED);
        pi_log_d("state_manage_access_set:STATE_TYPE_ACCESSED\n");
    }
    else
    {
        state_manage_access_set(STATE_TYPE_UNACCESS);
        pi_log_d("state_manage_access_set:STATE_TYPE_UNACCESS\n");
    }

    //销毁定时器
    if ( -1 == state_timer_destroy(&state_access_timer) )
    {
        pi_log_e("state_timer_destroy error! \n");
    }

    return ret;
}

static void login_timeout_callback (void *data)
{
    pi_log_d("login_timeout_callback:STATE_TYPE_UNLOGIN\n");
    //状态改为未接入
    state_manage_login_set(STATE_TYPE_UNLOGIN);

    //销毁定时器
    state_timer_destroy(&state_login_timer);
}

int32_t state_login_password(STATE_LOGINMETHOD_E login_method, char *data)
{
    int32_t      status = 0;
    ROUTER_MSG_S sendMsg;

    pi_log_d(" input data = {%s}-STATE_TYPE_LOGGING_IN\n", data);
    //状态改为登录中
    state_manage_login_set(STATE_TYPE_LOGGING_IN);

    //设置定时器的超时时长
    struct timeval timeout_arg = {
        .tv_sec  = 30,
        .tv_usec = 0,
    };

    int32_t ret = state_timer_init(&state_login_timer, login_timeout_callback, NULL, timeout_arg, 0);

    if ( -1 == ret )
    {
        pi_log_e("state_timer_init error\n");
        return STATE_RET_FAIL;
    }

    pi_log_d("bCircu = %d\n",state_login_timer.bCircu);
    if( -1 == state_timer_start(&state_login_timer))
    {
        pi_log_e("state_timer_start error\n");
        return STATE_RET_FAIL;
    }

    return STATE_RET_OK;
}

STATE_LOGIN_TYPE_E state_login_result_pro(char *buf)
{
    char         usr_name[64];
    cJSON*       json_item, *usrid, *usrname, *errcode, *login_mode;
    int32_t      status = 0;
    int32_t      ret = 0;

    pi_log_d("state_login_result_pro\n");
    pi_memset(usr_name, 0, sizeof(usr_name));
    json_item = cJSON_Parse(buf);
    if ( json_item == NULL )
    {
        pi_log_e("cJSON_GetErrorPtr: {%s}\n", cJSON_GetErrorPtr());
        return STATE_TYPE_LOG_MAX;
    }

    login_mode = cJSON_GetObjectItem(json_item, "LoginMode");
    if(login_mode != NULL)
    {
        pi_log_d("login_mode = %d\n",login_mode->valueint);

    }

    usrid = cJSON_GetObjectItem(json_item, "UserID");
    if ( usrid != NULL )
    {
        pi_log_d("UserID = %d\n", usrid->valueint);
        pthread_spin_lock(&spin_user_id);
        m_user_id = usrid->valueint;
        pthread_spin_unlock(&spin_user_id);
    }

    usrname = cJSON_GetObjectItem(json_item, "UserName");
    if ( usrname != NULL )
    {
        pi_log_d("usrname = {%s}\n", usrname->valuestring);
        pi_strncpy(usr_name, usrname->valuestring, (64 > pi_strlen(usrname->valuestring))? pi_strlen(usrname->valuestring):64);
    }

    errcode = cJSON_GetObjectItem(json_item, "ErrorCode");
    if ( errcode != NULL )
    {
        pi_log_d("ErrorCode = %d\n",errcode->valueint);
        if ( errcode->valueint == 0 )
        {
            state_manage_login_set(STATE_TYPE_LOGGED_IN);

            //销毁定时器
            if(login_mode != NULL && login_mode->valueint == STATE_LOGINMETHOD_ACCOUNT)
            {
                state_timer_destroy(&state_login_timer);
            }

        }
        else
        {
            state_manage_login_set(STATE_TYPE_UNLOGIN);
        }
    }

    usrname = cJSON_GetObjectItem(json_item, "UserName");
    if ( usrname != NULL )
    {
        pi_log_d("usrname = {%s}\n", usrname->valuestring);
        pi_strncpy(usr_name, usrname->valuestring, (64 > pi_strlen(usrname->valuestring))? pi_strlen(usrname->valuestring):64);
    }

    cJSON_Delete(json_item);
    json_item = NULL;

    return state_manage_login_get();  /// STATE_LOGIN_TYPE_E
}

int32_t state_logout_password(void)
{
    int32_t status = 0;
    ROUTER_MSG_S   sendMsg;

    //状态改为未登录
    state_manage_login_set(STATE_TYPE_UNLOGIN);

    //清空用户名
    authority_adapt_reset_usrname();

    //sendMsg 通知面板登出
    sendMsg.msgType = MSG_PRNSDK_LOGOUT_PANEL;
    sendMsg.msg1 = 0;
    sendMsg.msg2 = 0;
    sendMsg.msg3 = NULL;
    sendMsg.msgSender = MID_PRNSDK_STATE_MGR;
    status = task_msg_send_by_router( MID_PANEL, &sendMsg );
    if ( 0 != status )
    {
        pi_log_e(" send MSG_PRNSDK_LOGOUT_PANEL Fail 0  %d\r\n", status);
        return STATE_RET_FAIL;
    }

    return STATE_RET_OK;
}

uint32_t state_get_userid (void)
{
    pthread_spin_lock(&spin_user_id);
    uint32_t user_id = m_user_id;
    pi_log_d("m_user_id:%u\n",m_user_id);
    pthread_spin_unlock(&spin_user_id);

    return user_id;
}

int32_t state_manage_reset (void)
{
    int32_t status = 0;
    ROUTER_MSG_S   sendMsg;

    pi_log_d("reset all state\n");
    state_manage_access_set(STATE_TYPE_UNACCESS);
    state_manage_login_set(STATE_TYPE_UNLOGIN);

    //sendMsg 通知面板
    sendMsg.msgType = MSG_PRNSDK_ENABLE_CLOSE;
    sendMsg.msg1    = 0;
    sendMsg.msg2    = 0;
    sendMsg.msg3    = NULL;
    sendMsg.msgSender = MID_PRNSDK_STATE_MGR;
    status = task_msg_send_by_router( MID_PANEL, &sendMsg );
    if ( 0 != status )
    {
        pi_log_e(" send MSG_PRNSDK_ENABLE_CLOSE Fail 0  %d\r\n", status);
        return STATE_RET_FAIL;
    }
    return STATE_RET_OK;
}

/**
 * @}
 */

