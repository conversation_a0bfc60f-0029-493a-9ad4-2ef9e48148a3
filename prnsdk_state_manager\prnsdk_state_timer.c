/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file prnsdk_state_timer.c
 * @addtogroup mainapp
 * @{
 * @brief PRINT SDK state timer module
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-09
 */
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <stdio.h>
#include <stdint.h>
#include <sys/eventfd.h>
#include "prnsdk_state_timer.h"
#include "pol/pol_io.h"
#include "pol/pol_log.h"

static void* state_timer_thread (void *arg)
{
    STATE_TIMER_S *stimer  = (STATE_TIMER_S*)arg;
    unsigned long long val = 0;
    struct timeval timeout;
    int32_t ret   = 0;
    int32_t start = 0;
    fd_set rset;                ///< 句柄集合
    int32_t read_ret;

    pi_log_d("State timer thread started (fd: %d).", stimer->tfd);

    FD_ZERO(&rset);
    FD_SET(stimer->tfd , &rset);      ///< 将定时器句柄tf加入集合中
    timeout = stimer->time_arg;
    while ( 1 )
    {
        pi_mutex_lock(stimer->mtx);
        if ( stimer->bExit )
        {
            pi_mutex_unlock(stimer->mtx);
            break;
        }
        timeout = stimer->time_arg;
        start = stimer->bStart;
        pi_mutex_unlock(stimer->mtx);

        ret = pi_select(stimer->tfd + 1 , &rset , NULL , NULL , start?&timeout:NULL);
        if ( ret == -1 )    ///< select error
        {
            pi_log_e("Select error in state timer thread: %s (errno %d)" , strerror(errno), errno);
            break;
        }
        else if ( ret == 0 ) // Timeout
        {
            stimer->timeout_callback(stimer->arg);

            FD_ZERO(&rset);
            FD_SET(stimer->tfd , &rset);

            pi_mutex_lock(stimer->mtx);
            timeout = stimer->time_arg;
            if ( !stimer->bCircu )
            {
                stimer->bStart = 0;
            }

            pi_mutex_unlock(stimer->mtx);
        }
        else // Event occurred on tfd
        {
            if (FD_ISSET(stimer->tfd, &rset)) {
                read_ret = pi_read(stimer->tfd , &val , sizeof(val));
                if (read_ret < 0) {
                    pi_log_e("Read error on eventfd (%d): %s (errno %d)", stimer->tfd, strerror(errno), errno);
                    break; 
                } else if (read_ret != sizeof(val)) {
                     pi_log_w("Warning: Partial read (%d bytes) from eventfd (%d), expected %zu bytes.", read_ret, stimer->tfd, sizeof(val));
                }
            } else {
                 pi_log_w("Warning: select returned > 0 but timer fd %d was not set?", stimer->tfd);
            }
            FD_ZERO(&rset);
            FD_SET(stimer->tfd , &rset);
        }
    } ///< end while

    pi_log_d("State timer thread exiting.");
    return NULL;
}

int32_t state_timer_init(STATE_TIMER_S *stimer ,
                            void (*timeout_callback) (void *data) ,
                            void *arg ,
                            struct timeval timeout_arg ,
                            int32_t bCircu
                        )
{
    if ( !stimer || !timeout_callback || (timeout_arg.tv_sec <= 0 && timeout_arg.tv_usec <= 0) )
    {
        return -1;
    }

    stimer->timeout_callback = timeout_callback;
    stimer->tfd = eventfd(0 , EFD_CLOEXEC|EFD_NONBLOCK);
    if ( stimer->tfd == -1 )
    {   
        pi_log_e("Failed to create eventfd: %s (errno %d)" , strerror(errno), errno) ;
        return -1;
    }
    stimer->bCircu      = bCircu;
    stimer->bStart      = 0;
    stimer->bExit       = 0;
    stimer->time_arg    = timeout_arg;
    stimer->arg         = arg;
    stimer->mtx         = pi_mutex_create();
    if ( stimer->mtx == INVALIDMTX )
    {   
        pi_log_e("Failed to create mutex.");
        close(stimer->tfd); 
        stimer->tfd = -1;
        return -1;
    }
    stimer->timer_pid = INVALIDTHREAD;
    pi_log_d("bCircu = %d\n",stimer->bCircu);
    stimer->timer_pid = pi_thread_create(state_timer_thread, PI_NORMAL_STACK, NULL, PI_LEVEL_PRIORITY, stimer , "prnsdk_state_timer");
    if ( stimer->timer_pid == INVALIDTHREAD )
    {   
        pi_log_e("Failed to create state timer thread.");
        pi_mutex_destroy(stimer->mtx); 
        stimer->mtx = INVALIDMTX;
        close(stimer->tfd); 
        stimer->tfd = -1;
        return -1;
    }

    pi_log_d("State timer initialized successfully (fd: %d, circ: %d).", stimer->tfd, stimer->bCircu);
    return 0;
}

int32_t state_timer_start(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(stimer->mtx);
    if (stimer->bStart)
    {
        pi_mutex_unlock(stimer->mtx);
        return 0;
    }
    stimer->bStart = 1;
    pi_mutex_unlock(stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    return 0;
}

int32_t state_timer_active(STATE_TIMER_S *stimer)
{
    int32_t ret = 0;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(stimer->mtx);
    ret = stimer->bStart;
    pi_mutex_unlock(stimer->mtx);

    return ret;
}

int32_t state_timer_stop(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(stimer->mtx);
    if ( !stimer->bStart )
    {
        pi_mutex_unlock(stimer->mtx);
        return 0;
    }
    stimer->bStart = 0;
    stimer->bCircu = 0;
    pi_mutex_unlock(stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    return 0;
}

int32_t state_timer_restart(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if (!stimer)
    {
        return -1;
    }

    pi_mutex_lock(stimer->mtx);
    stimer->bStart = 0;
    pi_mutex_unlock(stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    pi_mutex_lock(stimer->mtx);
    stimer->bStart = 1;
    pi_mutex_unlock(stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    return 0;
}

int32_t state_timer_update(STATE_TIMER_S *stimer , struct timeval timeout_arg)
{
    if ( !stimer || (timeout_arg.tv_sec <= 0 && timeout_arg.tv_usec <= 0) )
    {
        return -1;
    }

    pi_mutex_lock(stimer->mtx);
    if  ( stimer->bStart )
    {
        pi_mutex_unlock(stimer->mtx);
        return -1;
    }

    stimer->time_arg = timeout_arg;
    pi_mutex_unlock(stimer->mtx);

    return 0;
}

int32_t state_timer_destroy(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if ( !stimer ) {
        pi_log_e("Error: stimer pointer is NULL.");
        return -1;
    }

    pi_log_d("Starting state timer destruction (Thread ID: %lu, Mutex: %p, fd: %d)...", (unsigned long)stimer->timer_pid, (void*)stimer->mtx, stimer->tfd);

    // 1. Signal thread to exit
    if (stimer->mtx != INVALIDMTX) {
        pi_mutex_lock(stimer->mtx); // Correct handle passed
        stimer->bStart = 0;
        stimer->bExit  = 1;
        pi_mutex_unlock(stimer->mtx); // Correct handle passed
    } else {
        pi_log_w("Warning: Mutex was already invalid when trying to destroy timer.");
        stimer->bExit = 1; // Ensure exit flag is set anyway
    }

    // 2. Wake up the thread if it's potentially waiting in select()
    if (stimer->tfd != -1) {
        pi_log_d("Writing to eventfd (%d) to wake up thread...", stimer->tfd);
        pi_write(stimer->tfd , &val , sizeof(val));
    } else {
         pi_log_w("Warning: eventfd was invalid or closed when trying to wake up thread.");
    }

    // 3. Wait a short period, hoping the thread exits (Reverting to pi_sleep as join is not suitable)
    pi_log_d("Waiting briefly for thread (%lu) to exit...", (unsigned long)stimer->timer_pid);
    pi_sleep(1); 

    // 4. Destroy the thread using the pol function
    if (stimer->timer_pid != INVALIDTHREAD)
    {
        pi_log_d("Destroying state_timer_thread (%lu) using pi_thread_destroy...", (unsigned long)stimer->timer_pid);
        // Use the platform abstraction layer's destroy function
        pi_thread_destroy(stimer->timer_pid); 
        // Note: Behavior of pi_thread_destroy (cleanup, termination) depends on its implementation.
        stimer->timer_pid = INVALIDTHREAD; // Mark thread as destroyed/invalid
    } else {
         pi_log_w("Warning: Thread ID was already invalid when trying to destroy.");
    }

    // 5. Destroy the mutex 
    // It's generally safer to destroy mutex *after* ensuring thread is gone,
    // but pi_thread_destroy's behavior is unknown. Keeping original order.
    if (stimer->mtx != INVALIDMTX)
    {
        pi_log_d("Destroying mutex (%p)...", (void*)stimer->mtx);
        pi_mutex_destroy(stimer->mtx); // Correct handle passed
        stimer->mtx = INVALIDMTX;
    } else {
         pi_log_w("Warning: Mutex was already invalid before destroying.");
    }

    // 6. Close the eventfd
    if (stimer->tfd != -1)
    {
        pi_log_d("Closing eventfd (%d)...", stimer->tfd);
        close(stimer->tfd); // Standard close
        stimer->tfd = -1;
    } else {
         pi_log_w("Warning: eventfd was already invalid or closed before closing.");
    }

    pi_log_d("State timer destruction complete.");

    return 0; 
}
/**
 * @}
 */


