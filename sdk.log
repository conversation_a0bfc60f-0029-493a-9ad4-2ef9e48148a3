[01:57:58.104] state_login_result_pro                           - [DEBUG]state_login_result_pro
[01:57:58.104] state_login_result_pro                           - [DEBUG]login_mode = 1
[01:57:58.104] state_login_result_pro                           - [DEBUG]UserID = 11
[01:57:58.104] state_login_result_pro                           - [DEBUG]usrname = {wjm}
[01:57:58.104] state_login_result_pro                           - [DEBUG]ErrorCode = 0
[01:57:58.104] state_manage_login_set                           - [DEBUG]login state change to 2
[01:57:58.104] state_timer_destroy                              - [DEBUG]Starting state timer destruction (Thread ID: 1994377336, Mutex: 0x76dfa6d0, fd: 140)...[01:57:58.104] state_timer_destroy                              - [DEBUG]Writing to eventfd (140) to wake up thread...[01:57:58.104] state_timer_destroy                              - [DEBUG]Waiting briefly for thread (1994377336) to exit...[01:57:58.104] state_timer_thread                               - [DEBUG]State timer thread exiting.[01:57:58.220] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 51662 to 443) accept, sockfd(143)
[01:57:58.220] ssl_create_server                                - [DEBUG]waiting for socket(143) to contirm the certificate of server...
[01:57:58.441] qio_ssl_create_custom                            - [DEBUG]create SSL(server 143) QIO<0x73c13c38>
[01:57:58.442] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Heartbeat)
[01:57:58.442] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Heartbeat) parms() body[2]("") from client(51662->443)
[01:57:58.443] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:57:58.457] http_task_process                                - [DEBUG]recv 0 bytes, client(51662->443) has been closed!
[01:57:58.457] http_task_process                                - [DEBUG]process task end from client(51662->443) err(0) end(1)
[01:57:58.457] qio_ssl_close                                    - [DEBUG]close SSL(server 143) QIO<0x73c13c38>
[01:57:58.457] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 51662 to 443) end, sockfd(143)
[osal/pol/os/Linux/pol_linux.c:101]thread(prnsdk_state_timer) cancel failed: 3<No such process>
[01:57:59.104] state_timer_destroy                              - [DEBUG]Destroying state_timer_thread (1994377336) using pi_thread_destroy...[01:57:59.104] state_timer_destroy                              - [DEBUG]Destroying mutex (0x76dfa6d0)...[01:57:59.104] state_timer_destroy                              - [DEBUG]Closing eventfd (140)...[01:57:59.104] state_timer_destroy                              - [DEBUG]State timer destruction complete.[01:57:59.104] state_login_result_pro                           - [DEBUG]usrname = {wjm}
[01:57:59.104] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 426,msg1 = 0,msg2 = 0,msgSender = 157
[01:57:59.104] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:57:59.104] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:31,data_size:5
[01:57:59.145] http_task_process                                - [DEBUG]recv 0 bytes, client(51660->443) has been closed!
[01:57:59.145] http_task_process                                - [DEBUG]process task end from client(51660->443) err(0) end(1)
[01:57:59.145] qio_ssl_close                                    - [DEBUG]close SSL(server 142) QIO<0x72d1e7b0>
[01:57:59.145] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 51660 to 443) end, sockfd(142)
[01:57:59.177] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 51678 to 443) accept, sockfd(140)
[01:57:59.178] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:57:59.399] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x72d1f0c8>
[01:57:59.401] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Capabilities)
[01:57:59.401] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Capabilities) parms() body[909]({"UserName":"wjm","UsbEnable":0,"PrintCapabilities":{"global_ctrl":1,"color_limit":1,"statistic_ctrl":{"hue":{"color_page_limit":5000,"mono_page_limit":5000}}},"ScanCapabilities":{"PushModule":1,"SaveScanResult":1,"FtpParm":{"ServerName":"************","LoginName":"PantumEWS","FtpPassword":"asd123465QWE","FtpPort":2121,"Anonymity":0,"ServerPath":""},"EmailParm":null,"SmbParm":null,"SaveFtpParm":{"ServerName":"","LoginName":"","FtpPassword":"","FtpPort":0,"Anonymity":0,"ServerPath":""},"SaveEmailParm":null,"SaveSmbParm":null},"CopyCapabilities":{"CopyFunctionEnable":1,"ColorCopyEnable":1,"hue":{"color_page_limit":5000,"mono_page_limit":5000},"SaveCopyResult":1,"FtpParm":{"ServerName":"************","LoginName":"PantumEWS","FtpPassword":"asd123465QWE","FtpPort":2121,"Anonymity":0,"ServerPath":""},"SmbParm":{"ServerName":"","ServerPath":"","LoginName":"","SmbPassword":"","SmbPort":0,"Anonymity":0}}}) from client(51678->443)
[01:57:59.401] authority_adapt_local_data_proc                  - [DEBUG]authority usrname:wjm
[01:57:59.401] authority_adapt_local_data_proc                  - [DEBUG]m_usb_enable:0
[01:57:59.401] authority_adapt_proc                             - [DEBUG]module_type  0.
[01:57:59.401] print_ctl_authority_get_authority_info           - [INFO]L1121:get the print authority info and parse 
[01:57:59.401] authority_adapt_get_usrname                      - [DEBUG]authority usrname:wjm
[01:57:59.401] print_ctl_authority_get_authority_info           - [INFO]L1130:get user name success,usr_name[wjm] 
[01:57:59.401] print_ctl_authority_print_authority_info         - [INFO]L1087:************************* print authority info ************************* 

[01:57:59.401] print_ctl_authority_print_authority_info         - [INFO]L1089:global_ctrl = 1 

[01:57:59.401] print_ctl_authority_print_authority_info         - [INFO]L1090:color_limit = 1 

[01:57:59.401] print_ctl_authority_print_authority_info         - [INFO]L1091:color_page_limit = 5000 

[01:57:59.401] print_ctl_authority_print_authority_info         - [INFO]L1092:mono_page_limit = 5000 

[01:57:59.401] print_ctl_authority_print_authority_info         - [INFO]L1093:************************************************************************ 

[01:57:59.401] print_app_status_get_best_status                 - [INFO]L489 :get print best status [2621441] <PRINT_STATUS_IDLE>
[01:57:59.402] print_ctl_authority_print_authority_info         - [INFO]L1087:************************* print authority info ************************* 

[01:57:59.402] print_ctl_authority_print_authority_info         - [INFO]L1089:global_ctrl = 1 

[01:57:59.402] print_ctl_authority_print_authority_info         - [INFO]L1090:color_limit = 1 

[01:57:59.402] print_ctl_authority_print_authority_info         - [INFO]L1091:color_page_limit = 5000 

[01:57:59.402] print_ctl_authority_print_authority_info         - [INFO]L1092:mono_page_limit = 5000 

[01:57:59.402] print_ctl_authority_print_authority_info         - [INFO]L1093:************************************************************************ 

[01:57:59.402] authority_adapt_proc                             - [DEBUG]module_type  1.
[01:57:59.402] ScanAuthorityParser                              - [INFO]************************* scan authority info ************************* 
[01:57:59.402] ScanPushParser                                   - [INFO]cjson_push :1
[01:57:59.402] ScanSaveParser                                   - [INFO]scan cjson_save :1
[01:57:59.402] ScanParmParser                                   - [INFO]ftpServerName = ************, ftpLoginName = PantumEWS,ftpPassword =asd123465QWE ,ftpPort = 2121,ftpAnonymity = 0
[01:57:59.402] ScanParmParser                                   - [INFO]cjson_email_addr parser NULL
[01:57:59.402] ScanParmParser                                   - [INFO]smb object NUll. i = 0
[01:57:59.402] ScanSaveParmParser                               - [INFO]ftpServerName = , ftpLoginName = ,ftpPassword = ,ftpPort = 0,ftpAnonymity = 0
[01:57:59.402] ScanAuthorityParser                              - [INFO]******************************************************************** 
[01:57:59.402] scan_sdk_send_auth_status                        - [INFO]send auth status to ui[01:57:59.402] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 430,msg1 = 0,msg2 = 0,msgSender = 72
[01:57:59.402] authority_adapt_proc                             - [DEBUG]module_type  2.
[01:57:59.402] authority_adapt_get_usrname                      - [DEBUG]authority usrname:wjm
[01:57:59.402] Json_parser_set_copy_authority                   - [DEBUG]CopyFunctionEnable=1
[01:57:59.402] Json_parser_set_copy_authority                   - [DEBUG]ColorCopyEnable=1
[01:57:59.402] Json_parser_papersize_limit                      - [DEBUG]color_page_limit=5000 
[01:57:59.402] Json_parser_papersize_limit                      - [DEBUG]mono_page_limit=5000 
[01:57:59.402] Json_parser_set_copy_authority                   - [DEBUG]SaveCopyResult=1
[01:57:59.402] prnsdk_capabilities                              - [DEBUG]login_ret: -- 1, authority_adapt_recv_data_proc ret: 0
[01:57:59.403] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:57:59.403] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:41,data_size:1652
[01:57:59.403] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 295,msg1 = 1,msg2 = 0,msgSender = 101
[01:57:59.404] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:42,data_size:1072
[01:57:59.404] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 296,msg1 = 1,msg2 = 0,msgSender = 101
[01:57:59.404] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:43,data_size:16
[01:57:59.435] http_task_process                                - [DEBUG]recv 0 bytes, client(51678->443) has been closed!
[01:57:59.435] http_task_process                                - [DEBUG]process task end from client(51678->443) err(0) end(1)
[01:57:59.435] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x72d1f0c8>
[01:57:59.435] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 51678 to 443) end, sockfd(140)
[01:57:59.440] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 51686 to 443) accept, sockfd(140)
[01:57:59.440] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:57:59.862] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x72d1e7b0>
[01:57:59.863] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Set)
[01:57:59.863] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Set) parms() body[128]({"LockTime":120,"SleepTime":300,"UrlSet":"http://************:5000/PantumEWS/Event","DomainName":"[]","ScanMode":2,"CopyMode":2}) from client(51686->443)
[01:57:59.863] prnsdk_set                                       - [DEBUG]login_ret: -- 0
[01:57:59.863] prnsdk_set                                       - [DEBUG]snprintf url_path http://************:5000/PantumEWS/Event
[01:57:59.863] prnsdk_check_sleeping_and_wakeup                 - [DEBUG][10d20003]state parameter value:1
[01:57:59.863] send_msg_to_panel_and_wait                       - [DEBUG]send msg to panel, waitting for panel to return ui_set_ret
[01:57:59.863] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 419,msg1 = 2,msg2 = 0,msgSender = 7
[01:57:59.864] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:17,data_size:129
[01:57:59.865] data_distribution                                - [INFO]cmd_type:7 , cmd_value:17 , msg_type:0
[01:57:59.865] other_cmd_process                                - [INFO]panel send other cmd : OTHER_CMD_SDK_CONFIGPRINTER
[01:57:59.865] thread_prnsdk                                    - [DEBUG]panel return ui_set_ret: 0
[01:58:00.864] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:00.870] http_task_process                                - [DEBUG]recv 0 bytes, client(51686->443) has been closed!
[01:58:00.870] http_task_process                                - [DEBUG]process task end from client(51686->443) err(0) end(1)
[01:58:00.870] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x72d1e7b0>
[01:58:00.870] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 51686 to 443) end, sockfd(140)
[01:58:01.461] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 51694 to 443) accept, sockfd(140)
[01:58:01.461] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:58:01.682] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x72d1f4b0>
[01:58:01.683] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Heartbeat)
[01:58:01.684] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Heartbeat) parms() body[2]("") from client(51694->443)
[01:58:01.684] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:01.715] http_task_process                                - [DEBUG]recv 0 bytes, client(51694->443) has been closed!
[01:58:01.715] http_task_process                                - [DEBUG]process task end from client(51694->443) err(0) end(1)
[01:58:01.715] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x72d1f4b0>
[01:58:01.715] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 51694 to 443) end, sockfd(140)
[01:58:04.719] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 46200 to 443) accept, sockfd(140)
[01:58:04.719] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:58:04.941] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x72d1e7b0>
[01:58:04.943] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Heartbeat)
[01:58:04.943] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Heartbeat) parms() body[2]("") from client(46200->443)
[01:58:04.943] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:04.945] http_task_process                                - [DEBUG]recv 0 bytes, client(46200->443) has been closed!
[01:58:04.945] http_task_process                                - [DEBUG]process task end from client(46200->443) err(0) end(1)
[01:58:04.945] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x72d1e7b0>
[01:58:04.945] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 46200 to 443) end, sockfd(140)
[01:58:07.949] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 46204 to 443) accept, sockfd(140)
[01:58:07.949] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:58:08.370] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x72d1f0c8>
[01:58:08.372] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Heartbeat)
[01:58:08.372] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Heartbeat) parms() body[2]("") from client(46204->443)
[01:58:08.372] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:08.391] http_task_process                                - [DEBUG]recv 0 bytes, client(46204->443) has been closed!
[01:58:08.391] http_task_process                                - [DEBUG]process task end from client(46204->443) err(0) end(1)
[01:58:08.391] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x72d1f0c8>
[01:58:08.391] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 46204 to 443) end, sockfd(140)
> POST /PantumEWS/Event HTTP/1.1
Host: ************:5000
Accept: */*
Content-Type: application/json
Content-Length: 64

< HTTP/1.1 200 OK
< Date: Mon, 11 Aug 2025 03:36:12 GMT
< Content-Type: application/json; charset=utf-8
< Server: Kestrel
< Transfer-Encoding: chunked
< 
> POST /PantumEWS/Event HTTP/1.1
Host: ************:5000
Accept: */*
Content-Type: application/json
Content-Length: 65

< HTTP/1.1 200 OK
< Date: Mon, 11 Aug 2025 03:36:12 GMT
< Content-Type: application/json; charset=utf-8
< Server: Kestrel
< Transfer-Encoding: chunked
< 
[01:58:08.612] data_distribution                                - [INFO]cmd_type:7 , cmd_value:38 , msg_type:0
[01:58:08.612] other_cmd_process                                - [INFO]panel send other cmd : OTHER_CMD_SDK_UPDATE_COPY_SCAN_JOB_LIST
[01:58:08.612] send_event_handle                                - [DEBUG]request_data((null)) request_type(UpdateCopyJobListScan)
[01:58:08.612] send_event_handle                                - [DEBUG] url_path http://************:5000/PantumEWS/Event
[01:58:08.612] send_event_handle                                - [DEBUG]sdk_event reply type({"RequestType":"UpdateCopyJobListScan","ProductID":"AA2A000023"})
[01:58:08.612] data_distribution                                - [INFO]cmd_type:7 , cmd_value:39 , msg_type:0
[01:58:08.612] other_cmd_process                                - [INFO]panel send other cmd : OTHER_CMD_SDK_UPDATE_COPY_PRINT_JOB_LIST
{"ErrorCode":0}[01:58:08.623] send_event_handle                                - [DEBUG]request_data((null)) request_type(UpdateCopyJobListPrint)
[01:58:08.623] send_event_handle                                - [DEBUG] url_path http://************:5000/PantumEWS/Event
[01:58:08.623] send_event_handle                                - [DEBUG]sdk_event reply type({"RequestType":"UpdateCopyJobListPrint","ProductID":"AA2A000023"})
[01:58:08.623] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 46218 to 443) accept, sockfd(144)
[01:58:08.624] ssl_create_server                                - [DEBUG]waiting for socket(144) to contirm the certificate of server...
{"ErrorCode":0}[01:58:08.633] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 46232 to 443) accept, sockfd(140)
[01:58:08.850] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:58:08.850] qio_ssl_create_custom                            - [DEBUG]create SSL(server 144) QIO<0x72d1f4b0>
[01:58:08.852] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/UpdateCopyJobListScan)
[01:58:08.852] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/UpdateCopyJobListScan) parms() body[139]({"UserId":11,"JobCount":2,"DocumentInfo":[{"TaskID":456,"ScanCount":1,"DocumentName":"3"},{"TaskID":457,"ScanCount":1,"DocumentName":"4"}]}) from client(46218->443)
[01:58:08.852] prnsdk_copy_job_list_scan                        - [DEBUG]pjson: -- {"UserId":11,"JobCount":2,"DocumentInfo":[{"TaskID":456,"ScanCount":1,"DocumentName":"3"},{"TaskID":457,"ScanCount":1,"DocumentName":"4"}]}

[01:58:08.852] prnsdk_check_sleeping_and_wakeup                 - [DEBUG][10d20003]state parameter value:1
[01:58:08.852] send_msg_to_panel_and_wait                       - [DEBUG]send msg to panel, waitting for panel to return ui_joblist_ret
[01:58:08.852] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 419,msg1 = 7,msg2 = 0,msgSender = 7
[01:58:08.852] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:21,data_size:139
[01:58:08.855] data_distribution                                - [INFO]cmd_type:7 , cmd_value:16 , msg_type:0
[01:58:08.855] other_cmd_process                                - [INFO]panel send other cmd : OTHER_CMD_SDK_UPDATEJOBLI
[01:58:08.855] thread_prnsdk                                    - [DEBUG]panel return ui_joblist_ret: 0
[01:58:09.071] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x73c141a0>
[01:58:09.072] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/UpdateCopyJobListPrint)
[01:58:09.072] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/UpdateCopyJobListPrint) parms() body[187]({"UserId":11,"JobCount":2,"DocumentInfo":[{"DocumentName":"1","JobID":454,"Hue":1,"PaperSize":"A4","PaperType":1},{"DocumentName":"2","JobID":455,"Hue":1,"PaperSize":"A4","PaperType":1}]}) from client(46232->443)
[01:58:09.072] prnsdk_copy_job_list_print                       - [DEBUG]pjson: -- {"UserId":11,"JobCount":2,"DocumentInfo":[{"DocumentName":"1","JobID":454,"Hue":1,"PaperSize":"A4","PaperType":1},{"DocumentName":"2","JobID":455,"Hue":1,"PaperSize":"A4","PaperType":1}]}

[01:58:09.073] prnsdk_check_sleeping_and_wakeup                 - [DEBUG][10d20003]state parameter value:1
[01:58:09.073] send_msg_to_panel_and_wait                       - [DEBUG]send msg to panel, waitting for panel to return ui_joblist_ret
[01:58:09.073] panel_sys_msg_thread                             - [INFO]panel_dc recv msgType = 419,msg1 = 8,msg2 = 0,msgSender = 7
[01:58:09.073] panel_send_data_u8                               - [DEBUG]dc send cmd to panel,head_type:7,head_id:22,data_size:187
[01:58:09.101] data_distribution                                - [INFO]cmd_type:7 , cmd_value:16 , msg_type:0
[01:58:09.101] other_cmd_process                                - [INFO]panel send other cmd : OTHER_CMD_SDK_UPDATEJOBLI
[01:58:09.101] thread_prnsdk                                    - [DEBUG]panel return ui_joblist_ret: 0
[01:58:09.852] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:09.860] http_task_process                                - [DEBUG]recv 0 bytes, client(46218->443) has been closed!
[01:58:09.860] http_task_process                                - [DEBUG]process task end from client(46218->443) err(0) end(1)
[01:58:09.860] qio_ssl_close                                    - [DEBUG]close SSL(server 144) QIO<0x72d1f4b0>
[01:58:09.860] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 46218 to 443) end, sockfd(144)
[01:58:10.073] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:10.086] http_task_process                                - [DEBUG]recv 0 bytes, client(46232->443) has been closed!
[01:58:10.086] http_task_process                                - [DEBUG]process task end from client(46232->443) err(0) end(1)
[01:58:10.086] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x73c141a0>
[01:58:10.086] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 46232 to 443) end, sockfd(140)
[01:58:11.395] net_socket_accept_connection                     - [DEBUG][tlssrv_thread_handler] connection(************ : 46238 to 443) accept, sockfd(140)
[01:58:11.395] ssl_create_server                                - [DEBUG]waiting for socket(140) to contirm the certificate of server...
[01:58:11.616] qio_ssl_create_custom                            - [DEBUG]create SSL(server 140) QIO<0x72d1bd08>
[01:58:11.618] prnsdk_process_headers                           - [DEBUG]url(/PrintSystem/Heartbeat)
[01:58:11.618] prnsdk_process_request                           - [DEBUG]method(POST) url(/PrintSystem/Heartbeat) parms() body[2]("") from client(46238->443)
[01:58:11.618] prnsdk_process_request                           - [DEBUG]reply json string({"ErrorCode":0})
[01:58:11.655] http_task_process                                - [DEBUG]recv 0 bytes, client(46238->443) has been closed!
[01:58:11.655] http_task_process                                - [DEBUG]process task end from client(46238->443) err(0) end(1)
[01:58:11.655] qio_ssl_close                                    - [DEBUG]close SSL(server 140) QIO<0x72d1bd08>
[01:58:11.655] net_socket_close_connection                      - [DEBUG][tlssrv_connection] connection(************ : 46238 to 443) end, sockfd(140)
[01:58:12.052] login_timeout_callback                           - [DEBUG]login_timeout_callback:STATE_TYPE_UNLOGIN
[01:58:12.052] state_manage_login_set                           - [DEBUG]login state change to 0
[01:58:12.052] state_timer_destroy                              - [DEBUG]Starting state timer destruction (Thread ID: 0, Mutex: (nil), fd: -1)...[01:58:12.052] state_timer_destroy                              - [WARNING]Warning: Mutex was already invalid when trying to destroy timer.[01:58:12.052] state_timer_destroy                              - [WARNING]Warning: eventfd was invalid or closed when trying to wake up thread.[01:58:12.052] state_timer_destroy                              - [DEBUG]Waiting briefly for thread (0) to exit...[01:58:13.052] state_timer_destroy                              - [WARNING]Warning: Thread ID was already invalid when trying to destroy.[01:58:13.052] state_timer_destroy                              - [WARNING]Warning: Mutex was already invalid before destroying.[01:58:13.052] state_timer_destroy                              - [WARNING]Warning: eventfd was already invalid or closed before closing.[01:58:13.052] state_timer_destroy                              - [DEBUG]State timer destruction complete.[01:58:13.052] pi_mutex_lock_ent                                - [ERROR][CALL:state_timer_thread] condition (pmtx == NULL || pmtx->os_mtx == NULL), return -1.
